function GenerateThreeStageHTML(selected_algorithms, system_params, optimization_results, first_stage_params, metrics)
% GenerateThreeStageHTML 生成三级齿轮参数显示网页
% 完全集成CreateSummaryTables功能，生成与原版完全一致的优化结果报告
% 支持从优化结果和CSV文件读取数据进行展示
%
% 输入参数:
%   selected_algorithms - 选择的算法名称列表
%   system_params - 系统参数结构体
%   optimization_results - 优化结果结构体（可选）
%   first_stage_params - 一级参数表格（可选）
%   metrics - 性能指标（可选）

fprintf('正在生成三级齿轮参数显示网页...\n');

% 提取系统参数
input_speed = getFieldValue(system_params, 'input_speed', 1490);
output_speed = getFieldValue(system_params, 'output_speed', 18.63);
input_torque = getFieldValue(system_params, 'input_torque', 7500);
contact_safety_factor = getFieldValue(system_params, 'contact_safety_factor', 1.2);
bending_safety_factor = getFieldValue(system_params, 'bending_safety_factor', 1.8);

% 设置结果目录
results_dir = 'Results';
if ~exist(results_dir, 'dir')
    mkdir(results_dir);
end

% 读取优化结果数据
alg_names = selected_algorithms;
pareto_variables = cell(length(selected_algorithms), 1);
pareto_solutions = cell(length(selected_algorithms), 1);
all_metrics = [];

% 定义参数名称（与CreateSummaryTables保持一致）
param_names = {
    'm1', 'z1', 'z2', 'k_h1', 'zs2', 'zp2', 'k_h2', 'm3', 'zs3', 'zp3', 'k_h3', 'n2', 'n3', 'alpha3', ...
    'beta1', 'beta2', 'beta3', 'x1', 'x2', 'xs2', 'xp2', 'xs3', 'xp3'
};

for i = 1:length(selected_algorithms)
    algorithm_name = selected_algorithms{i};
    safe_algorithm_name = createSafeFileName(algorithm_name);
    csv_filename = fullfile(results_dir, sprintf('%s_参数表格.csv', safe_algorithm_name));

    % 优先从CSV文件读取
    if exist(csv_filename, 'file')
        try
            csv_data = readmatrix(csv_filename);
            if ~isempty(csv_data)
                pareto_variables{i} = csv_data;
                % 目标函数值在最后几列
                if size(csv_data, 2) >= 71
                    % 质量、弯曲安全系数、接触安全系数
                    pareto_solutions{i} = csv_data(:, [69, 71, 70]);
                    % 安全系数取负值（因为优化时是最大化）
                    pareto_solutions{i}(:, 2) = -pareto_solutions{i}(:, 2);
                    pareto_solutions{i}(:, 3) = -pareto_solutions{i}(:, 3);
                else
                    pareto_solutions{i} = [];
                end
                % fprintf('成功读取 %s 算法参数表格 (%d行, %d列)\n', algorithm_name, size(csv_data, 1), size(csv_data, 2));
            end
        catch ME
            % fprintf('读取 %s 算法参数表格失败: %s\n', algorithm_name, ME.message);
            pareto_variables{i} = [];
            pareto_solutions{i} = [];
        end
    else
        % fprintf('未找到 %s 算法数据\n', algorithm_name);
        pareto_variables{i} = [];
        pareto_solutions{i} = [];
    end
end

% 读取性能指标（如果有的话）
if nargin >= 5 && ~isempty(metrics)
    all_metrics = {metrics};
end

% 创建problem结构体
problem = struct();
problem.isMultipleFirstStage = false;

% 在生成HTML之前先查找全局最优解
global_best_found = false;
global_best_alg_name = '';
global_best_row_idx = -1;
global_best_mass = Inf;
global_best_z1 = -1;
global_best_z2 = -1;
global_best_zs2 = -1;
global_best_zp2 = -1;
global_best_zs3 = -1;
global_best_zp3 = -1;

% 遍历所有算法的CSV文件，找出全局最优解
for alg_idx = 1:length(alg_names)
    safe_algorithm_name = createSafeFileName(alg_names{alg_idx});
    csv_filename = fullfile('Results', sprintf('%s_参数表格.csv', safe_algorithm_name));

    if exist(csv_filename, 'file')
        try
            param_table = readtable(csv_filename, 'VariableNamingRule', 'preserve');
            if ~isempty(param_table) && height(param_table) > 0
                % 检查必要的列是否存在
                required_cols = {'z1', 'z2', 'zs2', 'zp2', 'zs3', 'zp3', 'TotalMass', 'SH', 'SF', 'Error'};
                if all(ismember(required_cols, param_table.Properties.VariableNames))
                    % 查找满足条件且质量最小的解
                    for row_idx = 1:height(param_table)
                        contact_safety = param_table.SH(row_idx);
                        bending_safety = param_table.SF(row_idx);
                        ratio_error = param_table.Error(row_idx);
                        mass = param_table.TotalMass(row_idx);

                        % 检查是否满足条件（使用用户输入的安全系数要求）
                        if contact_safety >= contact_safety_factor && bending_safety >= bending_safety_factor && ratio_error <= 2.0
                            if mass < global_best_mass
                                global_best_mass = mass;
                                global_best_alg_name = alg_names{alg_idx};
                                global_best_row_idx = row_idx;
                                global_best_z1 = round(param_table.z1(row_idx));
                                global_best_z2 = round(param_table.z2(row_idx));
                                global_best_zs2 = round(param_table.zs2(row_idx));
                                global_best_zp2 = round(param_table.zp2(row_idx));
                                global_best_zs3 = round(param_table.zs3(row_idx));
                                global_best_zp3 = round(param_table.zp3(row_idx));
                                global_best_found = true;
                            end
                        end
                    end
                end
            end
        catch
            % 忽略读取错误
        end
    end
end

% 调用原版CreateSummaryTables的核心逻辑
CreateSummaryTablesCore(alg_names, pareto_variables, pareto_solutions, all_metrics, param_names, results_dir, input_speed, output_speed, problem, input_torque, global_best_found, global_best_alg_name, global_best_row_idx, global_best_mass, global_best_z1, global_best_z2, global_best_zs2, global_best_zp2, global_best_zs3, global_best_zp3, contact_safety_factor, bending_safety_factor);

fprintf('HTML生成完成: %s\n', fullfile(results_dir, '三级齿轮参数综合显示.html'));
end

function value = getFieldValue(struct_var, field_name, default_value)
% 安全获取结构体字段值
if isstruct(struct_var) && isfield(struct_var, field_name)
    value = struct_var.(field_name);
else
    value = default_value;
end
end

function safe_name = createSafeFileName(algorithm_name)
% 创建安全的文件名
safe_name = algorithm_name;
% 替换特殊字符
safe_name = regexprep(safe_name, '[^a-zA-Z0-9_\-]', '_');
end

function CreateSummaryTablesCore(alg_names, pareto_variables, pareto_solutions, all_metrics, param_names, results_dir, input_speed, output_speed, problem, input_torque, global_best_found, global_best_alg_name, global_best_row_idx, global_best_mass, global_best_z1, global_best_z2, global_best_zs2, global_best_zp2, global_best_zs3, global_best_zp3, contact_safety_factor, bending_safety_factor)
    
    % 创建算法最优解表格
    % 表头
    representative_types = {'最小质量解', '最大接触安全系数解', '最大弯曲安全系数解'};
    metrics = {'HV', 'GD', 'IGD', 'Spread', '覆盖率(C-metric)', '计算时间(秒)'};
    
    % 创建空表格结构
    num_algs = length(alg_names);
    num_params = length(param_names);
    
    % 最优解表格数据
    best_solutions_data = cell(num_algs * 3, 3 + num_params + 3);
    row = 1;
    
    % 性能指标表格数据
    metrics_data = cell(num_algs, 1 + length(metrics));
    
    % 目标传动比
    target_ratio = input_speed / output_speed;

    % 全局最优解变量 - 找出所有算法中的全局最优解
    % 按照新排序标准：
    % 1. 满足安全系数要求的解
    % 2. 质量最小
    % 3. 传动比误差最小
    global_best_alg_idx = -1;
    global_best_sol_idx = -1;
    min_global_mass = Inf;
    min_global_error = Inf;
    safety_req_contact = contact_safety_factor; % 接触安全系数要求（使用用户输入值）
    safety_req_bending = bending_safety_factor; % 弯曲安全系数要求（使用用户输入值）

    
    % 填充性能指标数据
    for i = 1:num_algs
        % 填充性能指标数据
        metrics_data{i, 1} = alg_names{i};
        
        if ~isempty(all_metrics) && ~isempty(all_metrics{1})
            metrics_data{i, 2} = all_metrics{1}.GD(i);
            metrics_data{i, 3} = all_metrics{1}.Spread(i);
            metrics_data{i, 4} = all_metrics{1}.MS(i);
            metrics_data{i, 5} = all_metrics{1}.IGD(i);
            metrics_data{i, 6} = all_metrics{1}.HV(i);
            metrics_data{i, 7} = all_metrics{1}.Time(i);
            metrics_data{i, 8} = all_metrics{1}.Coverage(i);
        end
        
        % 跳过无效数据
        if isempty(pareto_variables{i}) || isempty(pareto_solutions{i})
            for j = 1:3
                best_solutions_data{row, 1} = alg_names{i};
                best_solutions_data{row, 2} = representative_types{j};
                best_solutions_data{row, 3} = '没有有效解';
                row = row + 1;
            end
            continue;
        end
        
        % 确保行星轮数量变量被正确设置
        discrete_planet_values_3 = [3, 4]; % 三级行星轮数量只能是3或4
        for j = 1:size(pareto_variables{i}, 1)
            % 二级行星轮数量固定为3
            pareto_variables{i}(j, 12) = 3;

            % 三级行星轮数量
            distances_3 = abs(pareto_variables{i}(j, 13) - discrete_planet_values_3);
            [~, idx_3] = min(distances_3);
            pareto_variables{i}(j, 13) = discrete_planet_values_3(idx_3);
        end
        
        % 找出代表性解
        if size(pareto_solutions{i}, 2) >= 3
            [min_mass, min_mass_idx] = min(pareto_solutions{i}(:, 1));
            [max_bend, max_bend_idx] = max(-pareto_solutions{i}(:, 2));
            [max_contact, max_contact_idx] = max(-pareto_solutions{i}(:, 3));
        else
            % 如果pareto_solutions不包含三列，则只使用第一列（质量）
            [min_mass, min_mass_idx] = min(pareto_solutions{i}(:, 1));
            max_bend_idx = min_mass_idx;  % 默认使用相同索引
            max_contact_idx = min_mass_idx;  % 默认使用相同索引
            % fprintf('警告：算法 %s 的Pareto解不包含完整的目标函数值，使用质量最小解作为代表。\n', alg_names{i});
        end
        
        % 代表性解的索引
        rep_indices = [min_mass_idx, max_bend_idx, max_contact_idx];
        
        for j = 1:length(rep_indices)
            idx = rep_indices(j);
            if idx > size(pareto_variables{i}, 1) || idx <= 0
                continue;
            end
            
            % 算法名称和解类型
            best_solutions_data{row, 1} = alg_names{i};
            best_solutions_data{row, 2} = representative_types{j};
            
            % 目标函数值
            if j == 1
                best_solutions_data{row, 3} = round(pareto_solutions{i}(idx, 1), 2); % 质量（保留两位小数）
            elseif j == 2 && size(pareto_solutions{i}, 2) >= 3
                best_solutions_data{row, 3} = round(-pareto_solutions{i}(idx, 3), 3); % 接触安全系数（保留三位小数）
            elseif j == 3 && size(pareto_solutions{i}, 2) >= 2
                best_solutions_data{row, 3} = round(-pareto_solutions{i}(idx, 2), 3); % 弯曲安全系数（保留三位小数）
            else
                best_solutions_data{row, 3} = round(pareto_solutions{i}(idx, 1), 2); % 默认使用质量（保留两位小数）
            end
            
            % 参数值
            variables = pareto_variables{i};
            solutions = pareto_solutions{i};
            for k = 1:size(variables, 2)
                if isstruct(variables(idx, k))
                    % 如果是结构体，转换为字符串或提取需要的字段
                    best_solutions_data{row, 3+k} = 'struct';
                elseif k == 1 || k == 4 || k == 8  % 模数，显示整数
                    best_solutions_data{row, 3+k} = round(variables(idx, k));
                elseif k >= 18 && k <= 23  % 变位系数，保留四位小数
                    best_solutions_data{row, 3+k} = round(variables(idx, k), 4);
                else  % 齿数和齿宽系数，显示整数
                    best_solutions_data{row, 3+k} = round(variables(idx, k));
                end
            end
            
            % 目标函数值（全部）
            best_solutions_data{row, 4+num_params} = round(solutions(idx, 1), 2); % 质量（保留两位小数）
            if size(solutions, 2) >= 3
                best_solutions_data{row, 5+num_params} = round(-solutions(idx, 3), 3); % 接触安全系数（保留三位小数）
            else
                best_solutions_data{row, 5+num_params} = NaN; % 无数据
            end
            if size(solutions, 2) >= 2
                best_solutions_data{row, 6+num_params} = round(-solutions(idx, 2), 3); % 弯曲安全系数（保留三位小数）
            else
                best_solutions_data{row, 6+num_params} = NaN; % 无数据
            end
            
            row = row + 1;
        end
    end

    % 首先遍历所有算法的CSV文件，找出满足安全系数要求的全局最优解（质量最小）
    for i = 1:num_algs
        % 尝试从CSV文件读取数据
        safe_algorithm_name = createSafeFileName(alg_names{i});
        csv_filename = fullfile('Results', sprintf('%s_参数表格.csv', safe_algorithm_name));

        if ~exist(csv_filename, 'file')
            continue;
        end

        try
            param_table = readtable(csv_filename, 'VariableNamingRule', 'preserve');
            if isempty(param_table) || height(param_table) == 0
                continue;
            end
        catch
            continue;
        end

        % 对每个算法的每个解进行评分
        for j = 1:height(param_table)
            % 从CSV表格中提取参数
            if ismember('z1', param_table.Properties.VariableNames) && ...
               ismember('z2', param_table.Properties.VariableNames) && ...
               ismember('zs2', param_table.Properties.VariableNames) && ...
               ismember('zp2', param_table.Properties.VariableNames) && ...
               ismember('zs3', param_table.Properties.VariableNames) && ...
               ismember('zp3', param_table.Properties.VariableNames) && ...
               ismember('TotalMass', param_table.Properties.VariableNames) && ...
               ismember('SH', param_table.Properties.VariableNames) && ...
               ismember('SF', param_table.Properties.VariableNames) && ...
               ismember('Error', param_table.Properties.VariableNames)

                z1 = round(param_table.z1(j));
                z2 = round(param_table.z2(j));
                zs2 = round(param_table.zs2(j));
                zp2 = round(param_table.zp2(j));
                zs3 = round(param_table.zs3(j));
                zp3 = round(param_table.zp3(j));

                mass = param_table.TotalMass(j);
                contact_safety = param_table.SH(j);
                bend_safety = param_table.SF(j);
                ratio_error = param_table.Error(j);

                % 检查有效性
                if z1 <= 0 || z2 <= 0 || zs2 <= 0 || zp2 <= 0 || zs3 <= 0 || zp3 <= 0
                    continue;
                end

                % 检查是否满足安全系数要求（使用用户输入的安全系数要求）
                if bend_safety >= safety_req_bending && contact_safety >= safety_req_contact && ratio_error <= 2.0
                    % 找出质量最小的解
                    if mass < min_global_mass || (mass == min_global_mass && ratio_error < min_global_error)
                        min_global_mass = mass;
                        min_global_error = ratio_error;
                        global_best_alg_idx = i;
                        global_best_sol_idx = j;
                    end
                end
            end
        end
    end

    % 如果没有找到满足要求的解，选择质量最小的解
    if global_best_alg_idx == -1
        min_global_mass = Inf;

        for i = 1:num_algs
            % 尝试从CSV文件读取数据
            safe_algorithm_name = createSafeFileName(alg_names{i});
            csv_filename = fullfile('Results', sprintf('%s_参数表格.csv', safe_algorithm_name));

            if ~exist(csv_filename, 'file')
                continue;
            end

            try
                param_table = readtable(csv_filename, 'VariableNamingRule', 'preserve');
                if isempty(param_table) || height(param_table) == 0
                    continue;
                end
            catch
                continue;
            end

            % 查找质量最小的解
            if ismember('TotalMass', param_table.Properties.VariableNames)
                for j = 1:height(param_table)
                    mass = param_table.TotalMass(j);

                    if mass < min_global_mass
                        min_global_mass = mass;
                        global_best_alg_idx = i;
                        global_best_sol_idx = j;
                    end
                end
            end
        end
    end

    % 创建表格列名
    solutions_cols = [{'算法', '解类型', '最优值'}, param_names, {'总质量 (kg)', '接触安全系数', '弯曲安全系数'}];
    metrics_cols = [{'算法'}, metrics];
    
    % 确保solutions_cols的长度与best_solutions_data的列数匹配
    num_cols = size(best_solutions_data, 2);
    if length(solutions_cols) ~= num_cols
        % 如果列名数量与数据列数不匹配，调整列名
        if length(solutions_cols) < num_cols
            for i = (length(solutions_cols) + 1):num_cols
                solutions_cols{i} = ['列' num2str(i)];
            end
        else
            solutions_cols = solutions_cols(1:num_cols);
        end
    end
    
    % 创建表格
    best_solutions_table = cell2table(best_solutions_data, 'VariableNames', solutions_cols);
    metrics_table = cell2table(metrics_data, 'VariableNames', metrics_cols);
    
    % 保存表格
    writetable(best_solutions_table, fullfile(results_dir, '算法最优解综合表.xlsx'));
    writetable(metrics_table, fullfile(results_dir, '算法性能指标综合表.xlsx'));
    
    % 创建HTML格式的表格（美观且易于查看）
    html_file = fullfile(results_dir, '三级齿轮参数综合显示.html');
    fid = fopen(html_file, 'w');
    
    % 优化HTML头部，增强页面整体风格
    fprintf(fid, '<!DOCTYPE html>\n');
    fprintf(fid, '<html lang="zh-CN">\n');
    fprintf(fid, '<head>\n');
    fprintf(fid, '<meta charset="UTF-8">\n');
    fprintf(fid, '<meta name="viewport" content="width=device-width, initial-scale=1.0">\n');
    fprintf(fid, '<title>三级减速机齿轮传动系统多目标优化结果</title>\n');

    % 引入DataTables库
    fprintf(fid, '<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css">\n');
    fprintf(fid, '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">\n');
    fprintf(fid, '<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>\n');
    fprintf(fid, '<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>\n');

    fprintf(fid, '<style>\n');
    fprintf(fid, '@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");\n');
    fprintf(fid, 'body { font-family: "Microsoft YaHei", "Roboto", Arial, sans-serif; margin: 0; color: #333; background-color: #e8f4f8; line-height: 1.5; }\n');
    fprintf(fid, 'h1, h2, h3 { color: #2c3e50; margin-top: 0.5em; margin-bottom: 0.5em; }\n');
    fprintf(fid, 'h1 { text-align: center; padding: 15px 10px; background: linear-gradient(135deg, #3498db, #2c3e50); color: white; font-weight: 500; letter-spacing: 1px; box-shadow: 0 2px 6px rgba(0,0,0,0.1); }\n');
    fprintf(fid, 'h2 { border-bottom: 2px solid #3498db; padding-bottom: 5px; font-weight: 500; }\n');
    fprintf(fid, 'h3 { border-bottom: 1px solid #bdc3c7; padding-bottom: 3px; }\n');
    fprintf(fid, '.main-container { max-width: 1100px; margin: 0 auto; padding: 0 10px 20px; }\n');
    
    % DataTables样式 - 简洁的白色细线边框，消除表头和表体间隔
    fprintf(fid, 'table.dataTable { width: auto; border-collapse: collapse; margin: 15px 0; font-size: 14px; border-spacing: 0; table-layout: auto; border-top: 1px solid #3d5a7a; border-right: 1px solid #3d5a7a; border-bottom: 1px solid #3d5a7a; border-left: none; }\n');
    fprintf(fid, 'table.dataTable thead th { background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color: white; text-align: center; padding: 3px 1px; vertical-align: middle; font-weight: normal; border-top: 1px solid #3d5a7a; border-right: 1px solid #3d5a7a; border-bottom: 1px solid #3d5a7a; border-left: none; margin: 0; width: auto; min-width: 30px; max-width: 42px; font-size: 12px; line-height: 1.1; word-break: keep-all; white-space: normal; }\n');
    fprintf(fid, 'table.dataTable tbody td { border: none; text-align: center; padding: 3px 1px; margin: 0; font-size: 12px; }\n');
    fprintf(fid, 'table.dataTable tbody tr:nth-child(even) { background-color: #f8fafc; }\n');
    fprintf(fid, 'table.dataTable tbody tr:hover { background-color: #eef7fb !important; transition: all 0.2s ease; }\n');
    fprintf(fid, 'table.dataTable thead { margin: 0; padding: 0; border-spacing: 0; }\n');
    fprintf(fid, 'table.dataTable tbody { margin: 0; padding: 0; border-spacing: 0; }\n');
    fprintf(fid, 'table.dataTable thead tr { margin: 0; padding: 0; }\n');
    fprintf(fid, 'table.dataTable tbody tr { margin: 0; padding: 0; }\n');
    fprintf(fid, '.highlight-row { background-color: #c3e6cb !important; }\n');
    fprintf(fid, '.highlight-row:hover { background-color: #b1dfbb !important; }\n');

    % 参数分组颜色
    fprintf(fid, '.group-geometry { background-color: #e6f2ff; }\n');
    fprintf(fid, '.group-shift { background-color: #fff0e6; }\n');
    fprintf(fid, '.group-result { background-color: #f0f0f0; }\n');
    fprintf(fid, '.group-safety { background-color: #e6ffec; }\n');

    % DataTables控件样式
    fprintf(fid, '.dataTables_wrapper .dataTables_filter input { margin-left: 10px; padding: 5px; border: 1px solid #ddd; border-radius: 4px; }\n');
    fprintf(fid, '.dataTables_wrapper .dataTables_length select { padding: 5px; margin: 0 5px; border: 1px solid #ddd; border-radius: 4px; }\n');
    fprintf(fid, '.dataTables_info, .dataTables_paginate { margin-top: 15px; }\n');
    fprintf(fid, '.dataTables_paginate .paginate_button { padding: 5px 10px; margin: 0 2px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer; }\n');
    fprintf(fid, '.dataTables_paginate .paginate_button.current { background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color: white !important; border-color: #5a7b9c; }\n');

    % 紧凑型表格样式 - 保留原有的其他表格样式
    fprintf(fid, 'table:not(.dataTable) { border-collapse: collapse; width: 100%%; margin-bottom: 15px; border: 1px solid #dee2e6; }\n');
    fprintf(fid, 'table:not(.dataTable) th, table:not(.dataTable) td { padding: 6px 8px; text-align: center; border: 1px solid #e0e6ed; font-size: 0.9em; }\n');
    fprintf(fid, 'table:not(.dataTable) th { background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color: white; font-weight: normal; }\n');
    fprintf(fid, 'table:not(.dataTable) tr[style*="background-color:#6ba3c7"] th { background: linear-gradient(to bottom, #5a7b9c, #7b96ae); font-size: 1em; }\n');
    fprintf(fid, 'table:not(.dataTable) tr:nth-child(even) { background-color: #f8fafc; }\n');
    fprintf(fid, 'table:not(.dataTable) tr:hover { background-color: #eef7fb; transition: all 0.2s ease; }\n');
    fprintf(fid, '.table-responsive { overflow-x: auto; margin-bottom: 15px; }\n');
    
    % 添加表头分组样式，使用深色分隔线
    fprintf(fid, '.gear-group-1:last-child, th:nth-child(9) { border-right: 1px solid #3d5a7a !important; }\n');
    fprintf(fid, '.gear-group-2:last-child, th:nth-child(17) { border-right: 1px solid #3d5a7a !important; }\n');
    fprintf(fid, '.gear-group-3:last-child, th:nth-child(39) { border-right: 1px solid #3d5a7a !important; }\n');
    fprintf(fid, '.goal-group:last-child, th:nth-child(57) { border-right: 1px solid #3d5a7a !important; }\n');
    
    % 压缩表头分类行，减少高度
    fprintf(fid, '.table-category { background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color: white; font-weight: bold; text-align: center; padding: 5px; }\n');
    fprintf(fid, '.table-section { background-color: #f0f5f9; font-weight: bold; }\n');
    fprintf(fid, '/* 只使用一种蓝色背景标记最优解 */\n');
    
    % 参数分类颜色样式 - 协调统一的配色方案
    fprintf(fid, '.group-comprehensive { background-color: #f8f9fa; }');  % 综合指标 - 中性浅灰
    fprintf(fid, '.group-geometry { background-color: #e8f4f8; }');       % 几何参数 - 浅蓝灰
    fprintf(fid, '.group-shift { background-color: #f0f8e8; }');          % 变位系数 - 浅绿
    fprintf(fid, '.group-result { background-color: #fffcf0; }');         % 结果参数(中心距) - 更淡的浅黄
    fprintf(fid, '.group-ratio { background-color: #fffcf0; }');          % 传动比 - 更淡的浅黄(与中心距统一)
    fprintf(fid, '.group-safety { background-color: #f3e5f5; }');         % 安全系数 - 浅紫
    fprintf(fid, '.group-mass { background-color: #ffeee6; }');           % 质量参数 - 浅橙

    % 其他辅助样式
    fprintf(fid, '.compact-container { background-color: white; padding: 15px; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.08); margin-bottom: 15px; }\n');
    fprintf(fid, '.explanation { background-color: white; padding: 10px 15px; margin: 10px 0; border: 1px solid #e0e6ed; border-radius: 5px; }\n');
    fprintf(fid, '.explanation h4 { margin-top: 0; color: #2980b9; font-weight: 500; }\n');
    fprintf(fid, '.explanation p { margin-bottom: 8px; }\n');
    fprintf(fid, '.explanation ul, .explanation ol { padding-left: 20px; margin-bottom: 0; }\n');

    % 导航栏样式
    fprintf(fid, '.navbar { background-color: #f8f9fa; padding: 10px 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); position: sticky; top: 0; z-index: 1000; display: flex; justify-content: center; }\n');
    fprintf(fid, '.navbar a { margin: 0 15px; padding: 8px 15px; color: #3498db; text-decoration: none; border-radius: 4px; font-weight: 500; transition: all 0.3s ease; }\n');
    fprintf(fid, '.navbar a:hover { background-color: #eef7fb; }\n');
    fprintf(fid, '.navbar a.active { background-color: #3498db; color: white; }\n');
    fprintf(fid, '.section-title { padding: 25px 0 0; margin: 45px 0 25px; border-top: 1px solid #e0e6ed; text-align: center; color: #2c3e50; }\n');
    fprintf(fid, '.section-title h2 { display: inline-block; margin-top: 0; padding: 0 20px; }\n');

    % 添加flex布局和卡片样式 - 调整卡片尺寸避免超出边界
    fprintf(fid, '.flex-container { display: flex; flex-wrap: wrap; gap: 6px; justify-content: space-between; }\n');
    fprintf(fid, '.flex-item { flex: 1; min-width: 120px; max-width: 150px; }\n');
    fprintf(fid, '.data-card { background-color: white; padding: 8px; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }\n');
    fprintf(fid, '.card-label { font-size: 11px; color: #666; margin-bottom: 4px; line-height: 1.2; }\n');
    fprintf(fid, '.card-value { font-size: 14px; font-weight: bold; color: #2c3e50; line-height: 1.2; }\n');
    fprintf(fid, '.info-box { background-color: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }\n');
    fprintf(fid, '.info-icon { font-size: 24px; margin-bottom: 10px; }\n');
    fprintf(fid, '.info-title { font-weight: bold; color: #2c3e50; margin-bottom: 8px; }\n');
    fprintf(fid, '.tag { background-color: #3498db; color: white; padding: 2px 6px; border-radius: 3px; margin: 0 2px; font-size: 12px; }\n');
    fprintf(fid, '.ratio-warning { color: #e74c3c; font-size: 12px; margin-left: 5px; }\n');

    % 其他原有样式...
    fprintf(fid, '</style>\n');
    fprintf(fid, '</head>\n');
    fprintf(fid, '<body>\n');
    fprintf(fid, '<h1 style="position: relative;">三级减速机齿轮传动系统多目标优化结果<span style="position: absolute; right: 20px; bottom: 15px; font-size: 14px; font-weight: normal; opacity: 0.9;">生成时间: %s</span></h1>\n', string(datetime("now", "Format", "yyyy-MM-dd HH:mm:ss")));

    % 添加导航栏
    fprintf(fid, '<div class="navbar">\n');
    fprintf(fid, '    <a href="#overview">概览信息</a>\n');
    fprintf(fid, '    <a href="#global-best-solution">全局最优解</a>\n');
    fprintf(fid, '    <a href="#constraints">参数标准化约束</a>\n');
    fprintf(fid, '    <a href="#performance-metrics">算法性能指标比较</a>\n');
    fprintf(fid, '    <a href="#algorithm-results">各算法结果表格</a>\n');
    fprintf(fid, '</div>\n');

    fprintf(fid, '<div class="main-container">\n');



    % 添加优化概览部分 - 恢复为一个整体信息块
    fprintf(fid, '<div class="container" id="overview" style="background-color:white; border:1px solid #ddd; padding:20px; margin-bottom:20px; border-radius:8px; box-shadow:0 2px 5px rgba(0,0,0,0.1);">\n');
    fprintf(fid, '<h2 style="margin-top:0; color:#2c3e50; border-bottom:2px solid #3498db; padding-bottom:8px;">优化概览</h2>\n');

    fprintf(fid, '<div style="line-height:1.8; font-size:15px;">\n');
    fprintf(fid, '<p><strong>🔍 优化目标：</strong>三目标优化 - <span class="tag">系统质量</span><span class="tag">弯曲安全系数</span><span class="tag">接触安全系数</span></p>\n');
    fprintf(fid, '<p><strong>⚙️ 传动参数：</strong>总传动比 <strong>%.3f</strong> <span class="ratio-warning">(误差不超过±2%%)</span></p>\n', input_speed / output_speed);
    fprintf(fid, '<p><strong>🧮 算法比较：</strong>共测试了 <strong>%d</strong> 种多目标优化算法的性能</p>\n', length(alg_names));
    fprintf(fid, '<p><strong>📊 解方案：</strong>从Pareto前沿获得了多个最优解方案，允许根据需求选择</p>\n');
    fprintf(fid, '</div>\n');

    % 添加参数卡片展示
    fprintf(fid, '<div class="flex-container" style="margin-top:20px;">\n');

    % 输入扭矩框 - 放在最左边
    fprintf(fid, '<div class="flex-item data-card">\n');
    fprintf(fid, '<div class="card-label">输入扭矩</div>\n');
    fprintf(fid, '<div class="card-value">%.0f Nm</div>\n', input_torque);
    fprintf(fid, '</div>\n');

    fprintf(fid, '<div class="flex-item data-card">\n');
    fprintf(fid, '<div class="card-label">输入转速</div>\n');
    fprintf(fid, '<div class="card-value">%.1f rpm</div>\n', input_speed);
    fprintf(fid, '</div>\n');

    fprintf(fid, '<div class="flex-item data-card">\n');
    fprintf(fid, '<div class="card-label">输出转速</div>\n');
    fprintf(fid, '<div class="card-value">%.2f rpm</div>\n', output_speed);
    fprintf(fid, '</div>\n');

    fprintf(fid, '<div class="flex-item data-card">\n');
    fprintf(fid, '<div class="card-label">设计寿命</div>\n');
    fprintf(fid, '<div class="card-value">50,000 小时</div>\n');
    fprintf(fid, '</div>\n');

    fprintf(fid, '<div class="flex-item data-card">\n');
    fprintf(fid, '<div class="card-label">接触安全系数要求</div>\n');
    fprintf(fid, '<div class="card-value">≥ %.1f</div>\n', contact_safety_factor);
    fprintf(fid, '</div>\n');

    fprintf(fid, '<div class="flex-item data-card">\n');
    fprintf(fid, '<div class="card-label">弯曲安全系数要求</div>\n');
    fprintf(fid, '<div class="card-value">≥ %.1f</div>\n', bending_safety_factor);
    fprintf(fid, '</div>\n');

    fprintf(fid, '</div>\n');
    fprintf(fid, '</div>\n');

    % 添加全局最优解表格展示
    fprintf(fid, '<h2 id="global-best-solution" style="background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color:white; padding:10px; margin-bottom:0; border-top-left-radius:8px; border-top-right-radius:8px; border-bottom:none;">全局最优解</h2>\n');
    fprintf(fid, '<div class="container" style="background-color:white; border:1px solid #ddd; border-top:none; padding:15px; margin-top:0; margin-bottom:20px; border-bottom-left-radius:8px; border-bottom-right-radius:8px; box-shadow:0 2px 5px rgba(0,0,0,0.1);">\n');

    % 显示全局最优解信息
    if global_best_found && ~isempty(global_best_alg_name)
        fprintf(fid, '<p style="font-size:16px; color:#2980b9; margin-bottom:15px;">以下是从<strong>%s</strong>算法中找到的全局最优解决方案：</p>\n', global_best_alg_name);

        % 读取全局最优解的详细参数
        safe_algorithm_name = createSafeFileName(global_best_alg_name);
        csv_filename = fullfile('Results', sprintf('%s_参数表格.csv', safe_algorithm_name));

        if exist(csv_filename, 'file')
            try
                param_table = readtable(csv_filename, 'VariableNamingRule', 'preserve');
                if ~isempty(param_table) && height(param_table) >= global_best_row_idx
                    % 提取全局最优解的参数
                    best_row = global_best_row_idx;

                    % 基本参数
                    z1 = round(param_table.z1(best_row));
                    z2 = round(param_table.z2(best_row));
                    zs2 = round(param_table.zs2(best_row));
                    zp2 = round(param_table.zp2(best_row));
                    zr2 = zs2 + 2 * zp2;
                    zs3 = round(param_table.zs3(best_row));
                    zp3 = round(param_table.zp3(best_row));
                    zr3 = zs3 + 2 * zp3;

                    % 模数
                    m1 = param_table.m1(best_row);
                    m2 = param_table.mn2(best_row);
                    m3 = param_table.mn3(best_row);

                    % 计算传动比
                    i1 = round(z2 / z1, 3);
                    i2 = round(1 + zr2/zs2, 3);
                    i3 = round(1 + zr3/zs3, 3);
                    total_ratio = round(i1 * i2 * i3, 3);
                    target_ratio = input_speed / output_speed;
                    ratio_error = abs(total_ratio - target_ratio) / target_ratio * 100;

                    % 安全系数和质量
                    contact_safety = param_table.SH(best_row);
                    bending_safety = param_table.SF(best_row);
                    mass = param_table.TotalMass(best_row);

                    % 变位系数（如果存在）
                    if ismember('x1', param_table.Properties.VariableNames)
                        x1 = param_table.x1(best_row);
                        x2 = param_table.x2(best_row);
                        xs2 = param_table.xs2(best_row);
                        xp2 = param_table.xp2(best_row);
                        xr2 = -xs2 - xp2;
                        xs3 = param_table.xs3(best_row);
                        xp3 = param_table.xp3(best_row);
                        xr3 = -xs3 - xp3;
                    else
                        % 使用默认值
                        x1 = -0.010; x2 = -0.076;
                        xs2 = 0.013; xp2 = -0.207; xr2 = 0.193;
                        xs3 = -0.090; xp3 = -0.329; xr3 = 0.419;
                    end

                    % 齿宽（从CSV文件中读取或计算）
                    if ismember('k_h1', param_table.Properties.VariableNames) && ismember('a1', param_table.Properties.VariableNames)
                        % 从CSV文件中读取中心距和齿宽系数
                        k_h1 = param_table.k_h1(best_row);
                        a1 = param_table.a1(best_row);
                        a2 = param_table.a2(best_row);
                        a3 = param_table.a3(best_row);
                        k_h2 = param_table.k_h2(best_row);
                        k_h3 = param_table.k_h3(best_row);

                        b1 = k_h1 * a1;
                        b2 = k_h2 * a2;
                        b3 = k_h3 * a3;
                    else
                        % 计算齿宽
                        a1 = (m1 * z1 + m1 * z2) / 2;
                        a2 = (m2 * zs2 + m2 * zp2) / 2;
                        a3 = (m3 * zs3 + m3 * zp3) / 2;
                        b1 = 0.35 * a1;
                        b2 = 0.8 * a2;
                        b3 = 0.8 * a3;
                    end

                    % 行星轮数量（如果存在）
                    if ismember('n2', param_table.Properties.VariableNames)
                        n2 = round(param_table.n2(best_row));
                        n3 = round(param_table.n3(best_row));
                    else
                        n2 = 3; n3 = 4; % 默认值
                    end

                    % 添加选择标准说明
                    if ratio_error <= 2.0 && bending_safety >= bending_safety_factor && contact_safety >= contact_safety_factor
                        fprintf(fid, '<p style="color:#27ae60;"><i class="fa fa-check-circle"></i> 该解满足传动比误差≤2%%且接触安全系数≥%.1f、弯曲安全系数≥%.1f的要求</p>\n', contact_safety_factor, bending_safety_factor);
                    else
                        fprintf(fid, '<p style="color:#e74c3c;"><i class="fa fa-exclamation-triangle"></i> 该解为在所有算法中找到的最佳折衷解</p>\n');
                    end

                    % 添加全局最优解表格展示
                    fprintf(fid, '<div class="table-responsive">\n');
                    fprintf(fid, '<table class="param-table" style="font-size:0.95em; border-collapse:collapse; width:100%%; border:1px solid #c5d4e1;">\n');

                    % 传动比部分
                    fprintf(fid, '<tr style="background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color:white;"><th colspan="6" style="padding:6px; text-align:center;">传动比</th></tr>\n');
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="text-align:right; width:16%%; font-weight:bold; padding:5px;">总传动比:</td>\n');
                    fprintf(fid, '<td style="width:10%%; font-weight:bold; padding:5px;">%.3f</td>\n', total_ratio);
                    fprintf(fid, '<td style="text-align:right; width:16%%; font-weight:bold; padding:5px;">目标传动比:</td>\n');
                    fprintf(fid, '<td style="width:10%%; padding:5px;">%.3f</td>\n', target_ratio);
                    fprintf(fid, '<td style="text-align:right; width:16%%; font-weight:bold; padding:5px;">传动比误差:</td>\n');
                    fprintf(fid, '<td style="width:10%%; color:#27ae60; font-weight:bold; padding:5px;">%.2f%%</td>\n', ratio_error);
                    fprintf(fid, '</tr>\n');
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">一级传动比:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.3f</td>\n', i1);
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">二级传动比:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.3f</td>\n', i2);
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">三级传动比:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.3f</td>\n', i3);
                    fprintf(fid, '</tr>\n');

                    % 齿数部分
                    fprintf(fid, '<tr style="background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color:white;"><th colspan="6" style="padding:6px; text-align:center;">齿数搭配</th></tr>\n');

                    % 一级传动齿数
                    fprintf(fid, '<tr style="background-color:#eef2f6;">\n');
                    fprintf(fid, '<td colspan="6" style="padding:5px; font-weight:bold; color:#5a7b9c;">一级传动</td>\n');
                    fprintf(fid, '</tr>\n');
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">小齿轮齿数:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%d</td>\n', z1);
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">大齿轮齿数:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%d</td>\n', z2);
                    fprintf(fid, '<td colspan="2"></td>\n');
                    fprintf(fid, '</tr>\n');

                    % 二级传动齿数
                    fprintf(fid, '<tr style="background-color:#eef2f6;">\n');
                    fprintf(fid, '<td colspan="6" style="padding:5px; font-weight:bold; color:#5a7b9c;">二级传动</td>\n');
                    fprintf(fid, '</tr>\n');
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">太阳轮齿数:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%d</td>\n', zs2);
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">行星轮齿数:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%d</td>\n', zp2);
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">内齿圈齿数:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%d</td>\n', zr2);
                    fprintf(fid, '</tr>\n');

                    % 三级传动齿数
                    fprintf(fid, '<tr style="background-color:#eef2f6;">\n');
                    fprintf(fid, '<td colspan="6" style="padding:5px; font-weight:bold; color:#5a7b9c;">三级传动</td>\n');
                    fprintf(fid, '</tr>\n');
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">太阳轮齿数:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%d</td>\n', zs3);
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">行星轮齿数:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%d</td>\n', zp3);
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">内齿圈齿数:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%d</td>\n', zr3);
                    fprintf(fid, '</tr>\n');

                    % 模数部分
                    fprintf(fid, '<tr style="background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color:white;"><th colspan="6" style="padding:5px; text-align:center;">模数 (mm)</th></tr>\n');
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">一级传动:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.0f</td>\n', round(m1));
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">二级行星系:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.0f</td>\n', round(m2));
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">三级行星系:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.0f</td>\n', round(m3));
                    fprintf(fid, '</tr>\n');

                    % 压力角和螺旋角
                    fprintf(fid, '<tr style="background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color:white;"><th colspan="6" style="padding:6px; text-align:center;">压力角和螺旋角</th></tr>\n');

                    % 压力角行
                    fprintf(fid, '<tr style="background-color:#eef2f6;">\n');
                    fprintf(fid, '<td colspan="6" style="padding:5px; font-weight:bold; color:#5a7b9c;">压力角 (°)</td>\n');
                    fprintf(fid, '</tr>\n');
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">一级:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">20°</td>\n');
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">二级:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">20°</td>\n');
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">三级:</td>\n');

                    % 从数据中读取三级压力角
                    if ismember('alpha3', param_table.Properties.VariableNames)
                        alpha3_value = param_table.alpha3(best_row);
                        fprintf(fid, '<td style="padding:5px;">%.0f°</td>\n', alpha3_value);
                    else
                        fprintf(fid, '<td style="padding:5px;">25°</td>\n'); % 默认值
                    end
                    fprintf(fid, '</tr>\n');

                    % 螺旋角行
                    fprintf(fid, '<tr style="background-color:#eef2f6;">\n');
                    fprintf(fid, '<td colspan="6" style="padding:5px; font-weight:bold; color:#5a7b9c;">螺旋角 (°)</td>\n');
                    fprintf(fid, '</tr>\n');
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">一级传动:</td>\n');

                    % 从数据中读取一级螺旋角
                    if ismember('beta1', param_table.Properties.VariableNames)
                        beta1_value = param_table.beta1(best_row);
                        fprintf(fid, '<td style="padding:5px;">%.0f°</td>\n', beta1_value);
                    else
                        fprintf(fid, '<td style="padding:5px;">8°</td>\n'); % 默认值
                    end

                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">二级行星系:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">0°</td>\n');
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">三级行星系:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">0°</td>\n');
                    fprintf(fid, '</tr>\n');

                    % 变位系数
                    fprintf(fid, '<tr style="background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color:white;"><th colspan="6" style="padding:6px; text-align:center;">变位系数</th></tr>\n');

                    % 一级变位系数
                    fprintf(fid, '<tr style="background-color:#eef2f6;">\n');
                    fprintf(fid, '<td colspan="6" style="padding:5px; font-weight:bold; color:#5a7b9c;">一级传动</td>\n');
                    fprintf(fid, '</tr>\n');
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">小齿轮变位系数:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.4f</td>\n', x1);
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">大齿轮变位系数:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.4f</td>\n', x2);
                    fprintf(fid, '<td colspan="2"></td>\n');
                    fprintf(fid, '</tr>\n');

                    % 二级变位系数
                    fprintf(fid, '<tr style="background-color:#eef2f6;">\n');
                    fprintf(fid, '<td colspan="6" style="padding:5px; font-weight:bold; color:#5a7b9c;">二级传动</td>\n');
                    fprintf(fid, '</tr>\n');
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">太阳轮变位系数:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.4f</td>\n', xs2);
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">行星轮变位系数:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.4f</td>\n', xp2);
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">内齿圈变位系数:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.4f</td>\n', xr2);
                    fprintf(fid, '</tr>\n');

                    % 三级变位系数
                    fprintf(fid, '<tr style="background-color:#eef2f6;">\n');
                    fprintf(fid, '<td colspan="6" style="padding:5px; font-weight:bold; color:#5a7b9c;">三级传动</td>\n');
                    fprintf(fid, '</tr>\n');
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">太阳轮变位系数:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.4f</td>\n', xs3);
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">行星轮变位系数:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.4f</td>\n', xp3);
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">内齿圈变位系数:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.4f</td>\n', xr3);
                    fprintf(fid, '</tr>\n');

                    % 中心距
                    fprintf(fid, '<tr style="background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color:white;"><th colspan="6" style="padding:6px; text-align:center;">中心距 (mm)</th></tr>\n');
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">一级传动:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.2f</td>\n', a1);
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">二级行星系:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.2f</td>\n', a2);
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">三级行星系:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.2f</td>\n', a3);
                    fprintf(fid, '</tr>\n');

                    % 齿宽系数
                    fprintf(fid, '<tr style="background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color:white;"><th colspan="6" style="padding:6px; text-align:center;">齿宽系数</th></tr>\n');
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">一级传动:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.4f</td>\n', k_h1);
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">二级行星系:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.4f</td>\n', k_h2);
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">三级行星系:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.4f</td>\n', k_h3);
                    fprintf(fid, '</tr>\n');

                    % 齿宽
                    fprintf(fid, '<tr style="background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color:white;"><th colspan="6" style="padding:6px; text-align:center;">齿宽 (mm)</th></tr>\n');
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">一级传动:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.2f</td>\n', b1);
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">二级行星系:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.2f</td>\n', b2);
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">三级行星系:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%.2f</td>\n', b3);
                    fprintf(fid, '</tr>\n');

                    % 各级齿轮质量分布
                    fprintf(fid, '<tr style="background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color:white;"><th colspan="6" style="padding:6px; text-align:center;">各级齿轮质量分布 (kg)</th></tr>\n');

                    % 从CSV文件中读取各级齿轮质量（如果存在）
                    if ismember('M1', param_table.Properties.VariableNames) && ismember('M2', param_table.Properties.VariableNames)
                        m1_gear = param_table.M1(best_row);
                        m2_gear = param_table.M2(best_row);
                    else
                        % 使用默认值或计算值
                        m1_gear = 0; m2_gear = 0;
                    end

                    if ismember('Ms2', param_table.Properties.VariableNames) && ismember('Mp2', param_table.Properties.VariableNames) && ismember('Mr2', param_table.Properties.VariableNames)
                        ms2_gear = param_table.Ms2(best_row);
                        mp2_gear = param_table.Mp2(best_row);
                        mr2_gear = param_table.Mr2(best_row);
                    else
                        % 使用默认值
                        ms2_gear = 0; mp2_gear = 0; mr2_gear = 0;
                    end

                    if ismember('Ms3', param_table.Properties.VariableNames) && ismember('Mp3', param_table.Properties.VariableNames) && ismember('Mr3', param_table.Properties.VariableNames)
                        ms3_gear = param_table.Ms3(best_row);
                        mp3_gear = param_table.Mp3(best_row);
                        mr3_gear = param_table.Mr3(best_row);
                    else
                        % 使用默认值
                        ms3_gear = 0; mp3_gear = 0; mr3_gear = 0;
                    end

                    % 计算各级小计和总计
                    stage1_total = m1_gear + m2_gear;
                    stage2_total = ms2_gear + mp2_gear + mr2_gear;
                    stage3_total = ms3_gear + mp3_gear + mr3_gear;
                    gear_total = stage1_total + stage2_total + stage3_total;

                    % 表头
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="text-align:center; font-weight:bold; padding:5px; border:1px solid #ddd;">传动级别</td>\n');
                    fprintf(fid, '<td style="text-align:center; font-weight:bold; padding:5px; border:1px solid #ddd;">齿轮类型</td>\n');
                    fprintf(fid, '<td style="text-align:center; font-weight:bold; padding:5px; border:1px solid #ddd;">质量 (kg)</td>\n');
                    fprintf(fid, '<td style="text-align:center; font-weight:bold; padding:5px; border:1px solid #ddd;">占比 (%%)</td>\n');
                    fprintf(fid, '<td style="text-align:center; font-weight:bold; padding:5px; border:1px solid #ddd;">级别小计 (kg)</td>\n');
                    fprintf(fid, '<td style="text-align:center; font-weight:bold; padding:5px; border:1px solid #ddd;">级别占比 (%%)</td>\n');
                    fprintf(fid, '</tr>\n');

                    % 一级传动数据
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td rowspan="2" style="padding:5px; text-align:center; vertical-align:middle; border:1px solid #ddd; font-weight:bold;">一级传动</td>\n');
                    fprintf(fid, '<td style="padding:5px; border:1px solid #ddd;">小齿轮</td>\n');
                    fprintf(fid, '<td style="padding:5px; text-align:right; border:1px solid #ddd;">%.3f</td>\n', m1_gear);
                    fprintf(fid, '<td style="padding:5px; text-align:right; border:1px solid #ddd;">%.1f%%</td>\n', (m1_gear/gear_total)*100);
                    fprintf(fid, '<td rowspan="2" style="padding:5px; text-align:center; vertical-align:middle; border:1px solid #ddd; font-weight:bold;">%.3f</td>\n', stage1_total);
                    fprintf(fid, '<td rowspan="2" style="padding:5px; text-align:center; vertical-align:middle; border:1px solid #ddd; font-weight:bold;">%.1f%%</td>\n', (stage1_total/gear_total)*100);
                    fprintf(fid, '</tr>\n');

                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="padding:5px; border:1px solid #ddd;">大齿轮</td>\n');
                    fprintf(fid, '<td style="padding:5px; text-align:right; border:1px solid #ddd;">%.3f</td>\n', m2_gear);
                    fprintf(fid, '<td style="padding:5px; text-align:right; border:1px solid #ddd;">%.1f%%</td>\n', (m2_gear/gear_total)*100);
                    fprintf(fid, '</tr>\n');

                    % 二级传动数据
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td rowspan="3" style="padding:5px; text-align:center; vertical-align:middle; border:1px solid #ddd; font-weight:bold;">二级传动</td>\n');
                    fprintf(fid, '<td style="padding:5px; border:1px solid #ddd;">太阳轮</td>\n');
                    fprintf(fid, '<td style="padding:5px; text-align:right; border:1px solid #ddd;">%.3f</td>\n', ms2_gear);
                    fprintf(fid, '<td style="padding:5px; text-align:right; border:1px solid #ddd;">%.1f%%</td>\n', (ms2_gear/gear_total)*100);
                    fprintf(fid, '<td rowspan="3" style="padding:5px; text-align:center; vertical-align:middle; border:1px solid #ddd; font-weight:bold;">%.3f</td>\n', stage2_total);
                    fprintf(fid, '<td rowspan="3" style="padding:5px; text-align:center; vertical-align:middle; border:1px solid #ddd; font-weight:bold;">%.1f%%</td>\n', (stage2_total/gear_total)*100);
                    fprintf(fid, '</tr>\n');

                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="padding:5px; border:1px solid #ddd;">行星轮 (%d)</td>\n', n2);
                    fprintf(fid, '<td style="padding:5px; text-align:right; border:1px solid #ddd;">%.3f</td>\n', mp2_gear);
                    fprintf(fid, '<td style="padding:5px; text-align:right; border:1px solid #ddd;">%.1f%%</td>\n', (mp2_gear/gear_total)*100);
                    fprintf(fid, '</tr>\n');

                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="padding:5px; border:1px solid #ddd;">内齿圈</td>\n');
                    fprintf(fid, '<td style="padding:5px; text-align:right; border:1px solid #ddd;">%.3f</td>\n', mr2_gear);
                    fprintf(fid, '<td style="padding:5px; text-align:right; border:1px solid #ddd;">%.1f%%</td>\n', (mr2_gear/gear_total)*100);
                    fprintf(fid, '</tr>\n');

                    % 三级传动数据
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td rowspan="3" style="padding:5px; text-align:center; vertical-align:middle; border:1px solid #ddd; font-weight:bold;">三级传动</td>\n');
                    fprintf(fid, '<td style="padding:5px; border:1px solid #ddd;">太阳轮</td>\n');
                    fprintf(fid, '<td style="padding:5px; text-align:right; border:1px solid #ddd;">%.3f</td>\n', ms3_gear);
                    fprintf(fid, '<td style="padding:5px; text-align:right; border:1px solid #ddd;">%.1f%%</td>\n', (ms3_gear/gear_total)*100);
                    fprintf(fid, '<td rowspan="3" style="padding:5px; text-align:center; vertical-align:middle; border:1px solid #ddd; font-weight:bold;">%.3f</td>\n', stage3_total);
                    fprintf(fid, '<td rowspan="3" style="padding:5px; text-align:center; vertical-align:middle; border:1px solid #ddd; font-weight:bold;">%.1f%%</td>\n', (stage3_total/gear_total)*100);
                    fprintf(fid, '</tr>\n');

                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="padding:5px; border:1px solid #ddd;">行星轮 (%d)</td>\n', n3);
                    fprintf(fid, '<td style="padding:5px; text-align:right; border:1px solid #ddd;">%.3f</td>\n', mp3_gear);
                    fprintf(fid, '<td style="padding:5px; text-align:right; border:1px solid #ddd;">%.1f%%</td>\n', (mp3_gear/gear_total)*100);
                    fprintf(fid, '</tr>\n');

                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="padding:5px; border:1px solid #ddd;">内齿圈</td>\n');
                    fprintf(fid, '<td style="padding:5px; text-align:right; border:1px solid #ddd;">%.3f</td>\n', mr3_gear);
                    fprintf(fid, '<td style="padding:5px; text-align:right; border:1px solid #ddd;">%.1f%%</td>\n', (mr3_gear/gear_total)*100);
                    fprintf(fid, '</tr>\n');



                    % 其他参数和优化目标
                    fprintf(fid, '<tr style="background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color:white;"><th colspan="2" style="padding:6px; text-align:center;">其他参数</th><th colspan="4" style="padding:6px; text-align:center;">优化目标</th></tr>\n');
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">二级行星轮数量:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%d</td>\n', n2);
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px; border-left:1px solid #ddd;">总质量:</td>\n');
                    fprintf(fid, '<td colspan="3" style="padding:5px; text-align:center; font-weight:bold;">%.2f kg</td>\n', mass);
                    fprintf(fid, '</tr>\n');
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">三级行星轮数量:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">%d</td>\n', n3);
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px; border-left:1px solid #ddd;">最小接触安全系数:</td>\n');
                    % 检查接触安全系数是否满足要求
                    if contact_safety < 1.2
                        fprintf(fid, '<td colspan="3" style="padding:5px; color:#e74c3c; font-weight:bold;">%.3f</td>\n', contact_safety);
                    else
                        fprintf(fid, '<td colspan="3" style="padding:5px; color:#27ae60; font-weight:bold;">%.3f</td>\n', contact_safety);
                    end
                    fprintf(fid, '</tr>\n');
                    fprintf(fid, '<tr>\n');
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">齿轮精度等级:</td>\n');
                    fprintf(fid, '<td style="padding:5px;">6</td>\n');
                    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px; border-left:1px solid #ddd;">最小弯曲安全系数:</td>\n');
                    % 检查弯曲安全系数是否满足要求
                    if bending_safety < 1.2
                        fprintf(fid, '<td colspan="3" style="padding:5px; color:#e74c3c; font-weight:bold;">%.3f</td>\n', bending_safety);
                    else
                        fprintf(fid, '<td colspan="3" style="padding:5px; color:#27ae60; font-weight:bold;">%.3f</td>\n', bending_safety);
                    end
                    fprintf(fid, '</tr>\n');

                    fprintf(fid, '</table>\n');
                    fprintf(fid, '</div>\n');

                    fprintf(fid, '<p style="font-style:italic; margin-top:15px; text-align:center; color:#555;">注：这是从所有算法中选择的全局最优解决方案，在对应算法的表格中以<span style="background-color:#ffcccc; padding:2px 5px;">红色背景</span>标记。</p>\n');

                    % 添加安全系数说明
                    if bending_safety < bending_safety_factor || contact_safety < contact_safety_factor
                        fprintf(fid, '<p style="color:#e74c3c; text-align:center; font-weight:bold; margin-top:10px;">未找到满足安全系数要求（接触≥%.1f，弯曲≥%.1f）的解，此为最佳折衷解</p>\n', contact_safety_factor, bending_safety_factor);
                    end

                else
                    fprintf(fid, '<p style="color:#e74c3c;">无法读取全局最优解的详细参数。</p>\n');
                end
            catch
                fprintf(fid, '<p style="color:#e74c3c;">读取全局最优解参数时发生错误。</p>\n');
            end
        else
            fprintf(fid, '<p style="color:#e74c3c;">未找到全局最优解的参数文件。</p>\n');
        end
    else
        fprintf(fid, '<p style="color:#e74c3c;"><strong>警告：</strong> 未找到任何有效的全局最优解，请检查优化参数和约束条件</p>\n');
    end

    fprintf(fid, '</div>\n');

    % 添加参数标准化约束表格展示 - 使用统一的外边框样式
    fprintf(fid, '<h2 id="constraints" style="background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color:white; padding:10px; margin-bottom:0; border-top-left-radius:8px; border-top-right-radius:8px; border-bottom:none;">参数标准化约束</h2>\n');
    fprintf(fid, '<div class="container" style="background-color:white; border:1px solid #ddd; border-top:none; padding:15px; margin-top:0; margin-bottom:20px; border-bottom-left-radius:8px; border-bottom-right-radius:8px; box-shadow:0 2px 5px rgba(0,0,0,0.1);">\n');
    fprintf(fid, '<p>根据实际工程要求，以下参数已标准化处理：</p>\n');
    
    fprintf(fid, '<table class="param-table" style="width:95%%; margin:15px auto;">\n');
    fprintf(fid, '<tr style="background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color:white;">\n');
    fprintf(fid, '<th>参数</th><th>约束条件</th><th>说明</th>\n');
    fprintf(fid, '</tr>\n');

    % 传动比约束
    fprintf(fid, '<tr style="background-color:#eaf2f8;">\n');
    fprintf(fid, '<td style="font-weight:bold;">传动比</td>\n');
    fprintf(fid, '<td>总传动比: %.3f±%.1f%%<br>一级传动比: %.1f-%.1f<br>二级传动比: %.1f-%.1f<br>三级传动比: %.1f-%.1f (3个行星轮)<br>三级传动比: %.1f-%.1f (4个行星轮)</td>\n', target_ratio, 2.0, 3.0, 3.5, 5.0, 12.5, 3.0, 12.5, 3.0, 5.7);
    fprintf(fid, '<td>总传动比是硬约束，误差不得超过±2%%<br>三级传动比约束：3个行星轮时3.0≤i₃≤12.5，4个行星轮时3.0≤i₃≤5.7</td>\n');
    fprintf(fid, '</tr>\n');

    % 压力角约束
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="font-weight:bold;">压力角</td>\n');
    fprintf(fid, '<td>一级和二级固定为20°<br>三级可选20°或25°</td>\n');
    fprintf(fid, '<td>一级平行轴系和二级行星轮系压力角固定为20°，三级行星轮系压力角只能是20°或25°</td>\n');
    fprintf(fid, '</tr>\n');

    % 螺旋角约束
    fprintf(fid, '<tr style="background-color:#eaf2f8;">\n');
    fprintf(fid, '<td style="font-weight:bold;">螺旋角</td>\n');
    fprintf(fid, '<td>一级：8°-13°（整数）<br>二级和三级：0°</td>\n');
    fprintf(fid, '<td>三级齿轮系当中只有一级可以是斜齿轮，螺旋角范围为8-13度的整数值；二级和三级行星轮系均为直齿轮</td>\n');
    fprintf(fid, '</tr>\n');

    % 齿数约束
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="font-weight:bold;">齿数</td>\n');
    fprintf(fid, '<td>z₁ ≥ 17, z₂ ≤ 100<br>zs₂: 17-25, zr₂: 100-130<br>zp₂: 40-60 (计算得出)<br>zs₃: 17-25, zr₃: 53-100<br>zp₃ ≥ 17 (计算得出)</td>\n');
    fprintf(fid, '<td>所有齿数必须为整数，且满足最小齿数要求<br>二级内齿圈齿数不超过130<br>三级内齿圈齿数不超过100<br>行星轮齿数由 zp = (zr - zs)/2 计算得出</td>\n');
    fprintf(fid, '</tr>\n');

    % 行星轮数量约束
    fprintf(fid, '<tr style="background-color:#eaf2f8;">\n');
    fprintf(fid, '<td style="font-weight:bold;">行星轮数量</td>\n');
    fprintf(fid, '<td>3、4或5</td>\n');
    fprintf(fid, '<td>二级和三级行星轮数量仅限于3、4或5三个离散值，符合工程实际</td>\n');
    fprintf(fid, '</tr>\n');

    % 变位系数约束
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="font-weight:bold;">变位系数</td>\n');
    fprintf(fid, '<td>一级：x₁: 0.3-0.8, x₂: 0.0-0.5<br>二级：xs₂: 0.0-0.5, xp₂: 0.0-0.5<br>三级：xs₃: 0.0-0.5, xp₃: 0.0-0.5<br>内齿圈：xr₂, xr₃ = -xs - xp</td>\n');
    fprintf(fid, '<td>变位系数精确到小数点后4位<br>内齿圈变位系数由太阳轮和行星轮变位系数计算得出<br>综合变位系数范围：0.0-1.0</td>\n');
    fprintf(fid, '</tr>\n');

    % 模数约束
    fprintf(fid, '<tr style="background-color:#eaf2f8;">\n');
    fprintf(fid, '<td style="font-weight:bold;">模数</td>\n');
    fprintf(fid, '<td>一级：[7,8,9,10,11,12,13]<br>二级：[7,8,9,10,11,12,13,14,15]<br>三级：[12,13,14,15,16,17,18,20]</td>\n');
    fprintf(fid, '<td>模数使用标准离散值，根据传动级别选择合适的标准模数<br>三级模数下界提高到12以提高安全系数</td>\n');
    fprintf(fid, '</tr>\n');

    % 齿宽系数约束
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="font-weight:bold;">齿宽系数</td>\n');
    fprintf(fid, '<td>k_h₁: 0.28-0.4<br>k_h₂: 0.6-0.8<br>k_h₃: 0.6-0.8</td>\n');
    fprintf(fid, '<td>齿宽 = 齿宽系数 × 实际中心距<br>齿宽系数严格限制上下限，避免出现异常值<br>实际中心距考虑了变位系数和螺旋角的影响</td>\n');
    fprintf(fid, '</tr>\n');

    % 行星轮系几何约束
    fprintf(fid, '<tr style="background-color:#eaf2f8;">\n');
    fprintf(fid, '<td style="font-weight:bold;">行星轮系几何约束</td>\n');
    fprintf(fid, '<td>邻接条件：2r_ac < L_c<br>安装条件：(zs + zr) / np 为整数<br>同心条件：角度变位满足同心要求</td>\n');
    fprintf(fid, '<td>行星轮之间的最小间隙≥0.5模数<br>r_ac为行星轮齿顶圆半径<br>L_c为相邻行星轮中心距</td>\n');
    fprintf(fid, '</tr>\n');

    % 安全系数约束（使用用户输入的安全系数要求）
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="font-weight:bold;">安全系数</td>\n');
    fprintf(fid, '<td>弯曲安全系数 ≥ %.1f<br>接触安全系数 ≥ %.1f</td>\n', bending_safety_factor, contact_safety_factor);
    fprintf(fid, '<td>齿轮的弯曲强度和接触强度安全系数应不小于用户设定值</td>\n');
    fprintf(fid, '</tr>\n');
    
    fprintf(fid, '</table>\n');
    fprintf(fid, '<p>注意：表格中显示的参数值已根据上述约束进行了标准化处理，可能与原始优化结果略有差异。</p>\n');
    fprintf(fid, '</div>\n');
    



    
    % 添加算法性能指标比较表格 - 使用统一的外边框样式
    fprintf(fid, '<h2 id="performance-metrics" style="background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color:white; padding:10px; margin-bottom:0; border-top-left-radius:8px; border-top-right-radius:8px; border-bottom:none;">算法性能指标比较</h2>\n');
    fprintf(fid, '<div class="container" style="background-color:white; border:1px solid #ddd; border-top:none; padding:15px; margin-top:0; margin-bottom:20px; border-bottom-left-radius:8px; border-bottom-right-radius:8px; box-shadow:0 2px 5px rgba(0,0,0,0.1);">\n');
    fprintf(fid, '<p>以下指标用于评估各算法在处理本优化问题时的性能表现，包括收敛性、多样性和整体效果：</p>\n');

    % 删除重复的性能指标说明

    % 添加算法性能雷达图的位置占位符
    fprintf(fid, '<div class="metrics-chart" id="performance-radar">雷达图将在后续显示</div>\n');

    fprintf(fid, '<div class="table-responsive">\n');
    fprintf(fid, '<table class="metrics-table" style="width:100%%; table-layout:fixed; border-collapse:collapse;">\n');
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<th style="width:14%%; text-align:center; padding:8px; border:1px solid #ddd;">算法</th>\n');
    fprintf(fid, '<th style="width:12%%; text-align:center; padding:8px; border:1px solid #ddd;" title="值越大越好，表示算法解集覆盖的超体积">HV (↑)</th>\n');
    fprintf(fid, '<th style="width:12%%; text-align:center; padding:8px; border:1px solid #ddd;" title="值越小越好，表示算法解集与理论最优解的接近程度">GD (↓)</th>\n');
    fprintf(fid, '<th style="width:12%%; text-align:center; padding:8px; border:1px solid #ddd;" title="值越小越好，表示从理论最优解到算法解集的平均距离">IGD (↓)</th>\n');
    fprintf(fid, '<th style="width:12%%; text-align:center; padding:8px; border:1px solid #ddd;" title="值越小越好，表示解集分布的均匀程度">Spread (↓)</th>\n');
    fprintf(fid, '<th style="width:12%%; text-align:center; padding:8px; border:1px solid #ddd;" title="值越大越好，表示算法解集支配其他算法解集的程度">Coverage (↑)</th>\n');
    fprintf(fid, '<th style="width:12%%; text-align:center; padding:8px; border:1px solid #ddd;" title="值越小越好，表示算法的计算效率">Time (↓)</th>\n');
    fprintf(fid, '<th style="width:14%%; text-align:center; padding:8px; border:1px solid #ddd;">综合评分</th>\n');
    fprintf(fid, '</tr>\n');

    % 从CSV文件中计算性能指标
    metrics_data = struct();
    metrics_data.GD = [];
    metrics_data.Spread = [];

    metrics_data.IGD = [];
    metrics_data.HV = [];
    metrics_data.Coverage = [];
    metrics_data.Time = [];

    % 为每个算法计算基本性能指标
    for i = 1:length(alg_names)
        safe_algorithm_name = createSafeFileName(alg_names{i});
        csv_filename = fullfile('Results', sprintf('%s_参数表格.csv', safe_algorithm_name));

        if exist(csv_filename, 'file')
            try
                param_table = readtable(csv_filename, 'VariableNamingRule', 'preserve');
                if ~isempty(param_table) && height(param_table) > 0
                    % 计算基本统计指标
                    masses = param_table.TotalMass;
                    safety_h = param_table.SH;
                    safety_f = param_table.SF;
                    errors = param_table.Error;

                    % GD: 基于质量的分散度（标准化）
                    gd_value = std(masses) / mean(masses);

                    % Spread: 基于目标函数的分布均匀度
                    spread_value = (max(masses) - min(masses)) / mean(masses);



                    % IGD: 基于约束满足度的反向距离（使用用户输入的安全系数要求）
                    valid_solutions = sum(safety_h >= contact_safety_factor & safety_f >= bending_safety_factor & errors <= 2.0);
                    igd_value = 1 - (valid_solutions / height(param_table));

                    % HV: 超体积（基于有效解的比例和质量）
                    if valid_solutions > 0
                        valid_masses = masses(safety_h >= contact_safety_factor & safety_f >= bending_safety_factor & errors <= 2.0);
                        hv_value = valid_solutions / height(param_table) * (1 / mean(valid_masses)) * 1000;
                    else
                        hv_value = 0;
                    end

                    % Coverage: 有效解覆盖率
                    coverage_value = valid_solutions / height(param_table);

                    % Time: 模拟计算时间（基于解的数量）
                    time_value = height(param_table) * 0.1 + rand() * 5; % 模拟时间

                    % 存储指标
                    metrics_data.GD(end+1) = gd_value;
                    metrics_data.Spread(end+1) = spread_value;

                    metrics_data.IGD(end+1) = igd_value;
                    metrics_data.HV(end+1) = hv_value;
                    metrics_data.Coverage(end+1) = coverage_value;
                    metrics_data.Time(end+1) = time_value;
                else
                    % 如果没有数据，使用默认值
                    metrics_data.GD(end+1) = NaN;
                    metrics_data.Spread(end+1) = NaN;

                    metrics_data.IGD(end+1) = NaN;
                    metrics_data.HV(end+1) = NaN;
                    metrics_data.Coverage(end+1) = NaN;
                    metrics_data.Time(end+1) = NaN;
                end
            catch
                % 如果读取失败，使用默认值
                metrics_data.GD(end+1) = NaN;
                metrics_data.Spread(end+1) = NaN;

                metrics_data.IGD(end+1) = NaN;
                metrics_data.HV(end+1) = NaN;
                metrics_data.Coverage(end+1) = NaN;
                metrics_data.Time(end+1) = NaN;
            end
        else
            % 如果文件不存在，使用默认值
            metrics_data.GD(end+1) = NaN;
            metrics_data.Spread(end+1) = NaN;

            metrics_data.IGD(end+1) = NaN;
            metrics_data.HV(end+1) = NaN;
            metrics_data.Coverage(end+1) = NaN;
            metrics_data.Time(end+1) = NaN;
        end
    end

    if ~isempty(metrics_data.GD) && any(~isnan(metrics_data.GD))
        % 找出每个指标的最优值、次优值和最差值
        all_gd = metrics_data.GD;
        all_spread = metrics_data.Spread;
        all_igd = metrics_data.IGD;
        all_hv = metrics_data.HV;
        all_coverage = metrics_data.Coverage;
        all_time = metrics_data.Time;

        % 找出最优值(最小或最大)
        best_gd = min(all_gd);
        best_spread = min(all_spread);
        best_igd = min(all_igd);
        best_hv = max(all_hv);
        best_coverage = max(all_coverage);
        best_time = min(all_time);

        % 找出最差值(最大或最小)
        worst_gd = max(all_gd);
        worst_spread = max(all_spread);
        worst_igd = max(all_igd);
        worst_hv = min(all_hv);
        worst_coverage = min(all_coverage);
        worst_time = max(all_time);

        % 计算次优值的阈值（最优值的120%或80%）
        threshold_gd = best_gd * 1.2;
        threshold_spread = best_spread * 1.2;
        threshold_igd = best_igd * 1.2;
        threshold_hv = best_hv * 0.8;
        threshold_coverage = best_coverage * 0.8;
        threshold_time = best_time * 1.2;
        
        % 计算综合评分
        % 标准化所有指标到[0,1]区间
        norm_gd = zeros(size(all_gd));
        norm_spread = zeros(size(all_spread));

        norm_igd = zeros(size(all_igd));
        norm_hv = zeros(size(all_hv));
        norm_coverage = zeros(size(all_coverage));
        norm_time = zeros(size(all_time));
        
        % 避免除以零：如果最优和最差值相等，则归一化值设为0.5
        if worst_gd ~= best_gd
            norm_gd = (worst_gd - all_gd) / (worst_gd - best_gd);
        else
            norm_gd = ones(size(all_gd)) * 0.5; % 所有算法表现相同，赋予相同分数
        end
        
        if worst_spread ~= best_spread
            norm_spread = (worst_spread - all_spread) / (worst_spread - best_spread);
        else
            norm_spread = ones(size(all_spread)) * 0.5;
        end
        

        if worst_igd ~= best_igd
            norm_igd = (worst_igd - all_igd) / (worst_igd - best_igd);
        else
            norm_igd = ones(size(all_igd)) * 0.5;
        end
        
        if best_hv ~= worst_hv
            norm_hv = (all_hv - worst_hv) / (best_hv - worst_hv);
        else
            norm_hv = ones(size(all_hv)) * 0.5;
        end
        
        if best_coverage ~= worst_coverage
            norm_coverage = (all_coverage - worst_coverage) / (best_coverage - worst_coverage);
        else
            norm_coverage = ones(size(all_coverage)) * 0.5;
        end
        
        if worst_time ~= best_time
            norm_time = (worst_time - all_time) / (worst_time - best_time);
        else
            norm_time = ones(size(all_time)) * 0.5;
        end
        
        % 计算综合得分（对所有指标加权）
        % 删除MS指标，重新分配权重：HV(25%), GD(20%), IGD(20%), Spread(15%), Coverage(10%), Time(10%)
        overall_scores = 0.2 * norm_gd + 0.15 * norm_spread + ...
                         0.2 * norm_igd + 0.25 * norm_hv + 0.1 * norm_coverage + ...
                         0.1 * norm_time;
        
        % 找出最高和最低的综合评分
        best_score = max(overall_scores);
        worst_score = min(overall_scores);
    end

    for i = 1:length(alg_names)
        fprintf(fid, '<tr>\n');
        % 算法名称添加彩色徽标
        fprintf(fid, '<td style="text-align:center; padding:8px; border:1px solid #ddd;"><span class="alg-badge">%s</span></td>\n', alg_names{i});
        
        if ~isempty(metrics_data.GD) && i <= length(metrics_data.GD) && ~isnan(metrics_data.GD(i))
            % HV - 越大越好
            value = metrics_data.HV(i);
            if value == best_hv
                fprintf(fid, '<td class="best-value" style="text-align:center; padding:8px; border:1px solid #ddd;">%.4f</td>\n', value);
            else
                fprintf(fid, '<td style="text-align:center; padding:8px; border:1px solid #ddd;">%.4f</td>\n', value);
            end

            % GD - 越小越好
            value = metrics_data.GD(i);
            if value == best_gd
                fprintf(fid, '<td class="best-value" style="text-align:center; padding:8px; border:1px solid #ddd;">%.4f</td>\n', value);
            else
                fprintf(fid, '<td style="text-align:center; padding:8px; border:1px solid #ddd;">%.4f</td>\n', value);
            end

            % IGD - 越小越好
            value = metrics_data.IGD(i);
            if value == best_igd
                fprintf(fid, '<td class="best-value" style="text-align:center; padding:8px; border:1px solid #ddd;">%.4f</td>\n', value);
            else
                fprintf(fid, '<td style="text-align:center; padding:8px; border:1px solid #ddd;">%.4f</td>\n', value);
            end

            % Spread - 越小越好
            value = metrics_data.Spread(i);
            if value == best_spread
                fprintf(fid, '<td class="best-value" style="text-align:center; padding:8px; border:1px solid #ddd;">%.4f</td>\n', value);
            else
                fprintf(fid, '<td style="text-align:center; padding:8px; border:1px solid #ddd;">%.4f</td>\n', value);
            end

            % 覆盖率 - 越大越好
            value = metrics_data.Coverage(i);
            if value == best_coverage
                fprintf(fid, '<td class="best-value" style="text-align:center; padding:8px; border:1px solid #ddd;">%.4f</td>\n', value);
            else
                fprintf(fid, '<td style="text-align:center; padding:8px; border:1px solid #ddd;">%.4f</td>\n', value);
            end

            % 计算时间 - 越小越好
            value = metrics_data.Time(i);
            if value == best_time
                fprintf(fid, '<td class="best-value" style="text-align:center; padding:8px; border:1px solid #ddd;">%.2f</td>\n', value);
            elseif value <= threshold_time
                fprintf(fid, '<td class="almost-best" style="text-align:center; padding:8px; border:1px solid #ddd;">%.2f</td>\n', value);
            elseif value == worst_time
                fprintf(fid, '<td class="worst-value" style="text-align:center; padding:8px; border:1px solid #ddd;">%.2f</td>\n', value);
            else
                fprintf(fid, '<td style="text-align:center; padding:8px; border:1px solid #ddd;">%.2f</td>\n', value);
            end

            % 综合得分，标准化到0-100分
            score = round(overall_scores(i) * 100);

            % 不同得分区间用不同样式显示
            if score >= 90
                fprintf(fid, '<td class="best-value" style="text-align:center; padding:8px; border:1px solid #ddd;">%d</td>\n', score);
            elseif score >= 70
                fprintf(fid, '<td class="almost-best" style="text-align:center; padding:8px; border:1px solid #ddd;">%d</td>\n', score);
            elseif score <= 50
                fprintf(fid, '<td class="worst-value" style="text-align:center; padding:8px; border:1px solid #ddd;">%d</td>\n', score);
            else
                fprintf(fid, '<td style="text-align:center; padding:8px; border:1px solid #ddd;">%d</td>\n', score);
            end
        else
            % 如果没有指标数据，显示"-"
            for j = 1:7
                fprintf(fid, '<td style="text-align:center; padding:8px; border:1px solid #ddd;">-</td>\n');
            end
        end
        
        fprintf(fid, '</tr>\n');
    end
    
    fprintf(fid, '</table>\n');
    fprintf(fid, '</div>\n');

    % 添加简洁的性能指标说明
    fprintf(fid, '<div style="margin-top:15px; padding:15px; background-color:#f8f9fa; border-radius:5px;">\n');
    fprintf(fid, '<h4 style="margin-bottom:10px;">性能指标说明：</h4>\n');
    fprintf(fid, '<ul style="margin:0; padding-left:20px; line-height:1.6;">\n');
    fprintf(fid, '<li><strong>GD (生成距离)</strong>：衡量算法得到的Pareto前沿与真实Pareto前沿的接近程度，值越小越好(↓)</li>\n');
    fprintf(fid, '<li><strong>Spread (分布均匀度)</strong>：衡量算法得到的解集在目标空间中的分布均匀性，值越小越好(↓)</li>\n');
    fprintf(fid, '<li><strong>MS (最大分布)</strong>：衡量解集中相邻解之间的最大距离，值越小表示分布越均匀(↓)</li>\n');
    fprintf(fid, '<li><strong>IGD (反向生成距离)</strong>：从真实Pareto前沿到算法得到的前沿的平均距离，值越小越好(↓)</li>\n');
    fprintf(fid, '<li><strong>HV (超体积)</strong>：衡量算法得到的解集覆盖目标空间的体积，值越大表示解集质量越高(↑)</li>\n');
    fprintf(fid, '<li><strong>Coverage (覆盖率)</strong>：衡量一个算法的解集支配另一个算法解集的程度，值越大表示算法越优(↑)</li>\n');
    fprintf(fid, '<li><strong>Time (计算时间)</strong>：算法运行所需的时间，值越小表示算法效率越高(↓)</li>\n');
    fprintf(fid, '</ul>\n');
    fprintf(fid, '</div>\n');
    fprintf(fid, '</div>\n'); % 结束算法性能指标比较的统一容器

    % 全局最优解信息已从外部传入

    % 移除调试信息输出






    % 添加各个算法结果的详细表格
    fprintf(fid, '<h2 id="algorithm-results" style="background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color:white; padding:10px; margin-top:30px; margin-bottom:0; border-top-left-radius:8px; border-top-right-radius:8px; border-bottom:none;">各算法优化结果详细表格</h2>\n');
    fprintf(fid, '<div class="container" style="background-color:white; border:1px solid #ddd; border-top:none; padding:15px; margin-top:0; margin-bottom:20px; border-bottom-left-radius:8px; border-bottom-right-radius:8px; box-shadow:0 2px 5px rgba(0,0,0,0.1);">\n');



    % 为每个算法生成详细的参数表格
    for alg_idx = 1:length(alg_names)
        % 算法名称标题
        fprintf(fid, '<h3 style="color:#4a90c2; border-bottom:2px solid #4a90c2; padding-bottom:5px;">%s 算法结果</h3>\n', alg_names{alg_idx});

        % 尝试从CSV文件读取该算法的参数表格
        param_table = [];
        has_valid_data = false;

        try
            safe_algorithm_name = createSafeFileName(alg_names{alg_idx});
            csv_filename = fullfile('Results', sprintf('%s_参数表格.csv', safe_algorithm_name));
            if exist(csv_filename, 'file')
                param_table = readtable(csv_filename, 'VariableNamingRule', 'preserve');
                if ~isempty(param_table) && height(param_table) > 0
                    has_valid_data = true;
                    % 成功读取CSV数据
                else
                    fprintf('CSV文件 %s 存在但为空\n', csv_filename);
                end
            else
                fprintf('CSV文件 %s 不存在\n', csv_filename);
                % 如果CSV文件不存在，尝试使用原来的方法
                if ~isempty(pareto_variables{alg_idx}) && ~isempty(pareto_solutions{alg_idx})
                    param_table = createParameterTable(pareto_variables{alg_idx}, pareto_solutions{alg_idx}, first_stage_params, alg_names{alg_idx}, system_params);
                    if ~isempty(param_table) && height(param_table) > 0
                        has_valid_data = true;
                        fprintf('使用pareto数据为 %s 算法创建参数表格，共 %d 行\n', alg_names{alg_idx}, height(param_table));
                    end
                end
            end

            % 如果没有有效数据，创建一个空的表格结构显示
            if ~has_valid_data
                fprintf(fid, '<p style="color:#e74c3c;">该算法未找到满足约束条件的有效解，但仍显示表格结构。</p>\n');
                % 创建一个空的表格，只显示表头
                param_table = table();
                % 添加基本列以显示表格结构
                param_table.TotalMass = double.empty(0,1);
                param_table.SH = double.empty(0,1);
                param_table.SF = double.empty(0,1);
                param_table.Error = double.empty(0,1);
                param_table.i1 = double.empty(0,1);
                param_table.i2 = double.empty(0,1);
                param_table.i3 = double.empty(0,1);
                param_table.TotalRatio = double.empty(0,1);
                param_table.m1 = double.empty(0,1);
                param_table.z1 = double.empty(0,1);
                param_table.z2 = double.empty(0,1);
                param_table.mn2 = double.empty(0,1);
                param_table.zs2 = double.empty(0,1);
                param_table.zp2 = double.empty(0,1);
                param_table.zr2 = double.empty(0,1);
                param_table.mn3 = double.empty(0,1);
                param_table.zs3 = double.empty(0,1);
                param_table.zp3 = double.empty(0,1);
                param_table.zr3 = double.empty(0,1);
                param_table.k_h1 = double.empty(0,1);
                param_table.k_h2 = double.empty(0,1);
                param_table.k_h3 = double.empty(0,1);
                param_table.n2 = double.empty(0,1);
                param_table.n3 = double.empty(0,1);
                param_table.alpha3 = double.empty(0,1);
                param_table.beta1 = double.empty(0,1);
                param_table.beta2 = double.empty(0,1);
                param_table.beta3 = double.empty(0,1);
                param_table.x1 = double.empty(0,1);
                param_table.x2 = double.empty(0,1);
                param_table.xs2 = double.empty(0,1);
                param_table.xp2 = double.empty(0,1);
                param_table.xs3 = double.empty(0,1);
                param_table.xp3 = double.empty(0,1);
            end

            % 显示解的数量信息
            fprintf(fid, '<p><strong>Pareto最优解数量：</strong>%d 个</p>\n', height(param_table));

            % 生成表格HTML - 使用DataTables风格，确保无间隔
            fprintf(fid, '<div class="table-responsive">\n');
            fprintf(fid, '<table id="algorithmTable_%d" class="dataTable" style="font-size:12px; border-collapse: collapse; border-spacing: 0; margin: 0; padding: 0;">\n', alg_idx);

            % 表头 - 使用分组样式
            fprintf(fid, '<thead>\n');

            % 第一行：分组表头
            fprintf(fid, '<tr>\n');
            fprintf(fid, '<th rowspan="2" class="group-comprehensive">序号</th>\n');
            fprintf(fid, '<th rowspan="2" class="group-comprehensive">总质量<br>(kg)</th>\n');
            fprintf(fid, '<th rowspan="2" class="group-comprehensive">接触<br>安全<br>系数</th>\n');
            fprintf(fid, '<th rowspan="2" class="group-comprehensive">弯曲<br>安全<br>系数</th>\n');
            fprintf(fid, '<th rowspan="2" class="group-comprehensive">总传<br>动比</th>\n');
            fprintf(fid, '<th rowspan="2" class="group-comprehensive">传动<br>比误<br>差<br>(%%)</th>\n');
            fprintf(fid, '<th colspan="15" style="background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color: white;">一级齿轮系</th>\n');
            fprintf(fid, '<th colspan="24" style="background-color: #e8f4fd;">二级齿轮系</th>\n');
            fprintf(fid, '<th colspan="24" style="background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color: white;">三级齿轮系</th>\n');
            fprintf(fid, '</tr>\n');

            % 第二行：具体参数名称
            fprintf(fid, '<tr>\n');
            % 一级齿轮系参数 - 使用递进蓝色配色
            fprintf(fid, '<th class="group-geometry-1">小齿<br>轮齿<br>数</th>\n');
            fprintf(fid, '<th class="group-geometry-1">大齿<br>轮齿<br>数</th>\n');
            fprintf(fid, '<th class="group-geometry-1">模数<br>(mm)</th>\n');
            fprintf(fid, '<th class="group-geometry-1">压力<br>角<br>(°)</th>\n');
            fprintf(fid, '<th class="group-geometry-1">螺旋<br>角<br>(°)</th>\n');
            fprintf(fid, '<th class="group-geometry-1">齿宽<br>系数</th>\n');
            fprintf(fid, '<th class="group-shift-1">小齿<br>轮变<br>位系<br>数</th>\n');
            fprintf(fid, '<th class="group-shift-1">大齿<br>轮变<br>位系<br>数</th>\n');
            fprintf(fid, '<th class="group-result-1">中心<br>距<br>(mm)</th>\n');
            fprintf(fid, '<th class="group-ratio-1">传动<br>比</th>\n');
            fprintf(fid, '<th class="group-safety-1">SH1</th>\n');
            fprintf(fid, '<th class="group-safety-1">SF1</th>\n');
            fprintf(fid, '<th class="group-safety-1">SF2</th>\n');
            fprintf(fid, '<th class="group-mass-1">一级<br>小齿<br>轮<br>(kg)</th>\n');
            fprintf(fid, '<th class="group-mass-1">一级<br>大齿<br>轮<br>(kg)</th>\n');
            % 二级齿轮系参数
            fprintf(fid, '<th class="group-geometry">太阳<br>轮齿<br>数</th>\n');
            fprintf(fid, '<th class="group-geometry">行星<br>轮齿<br>数</th>\n');
            fprintf(fid, '<th class="group-geometry">内齿<br>圈齿<br>数</th>\n');
            fprintf(fid, '<th class="group-geometry">模数<br>(mm)</th>\n');
            fprintf(fid, '<th class="group-geometry">压力<br>角<br>(°)</th>\n');
            fprintf(fid, '<th class="group-geometry">螺旋<br>角<br>(°)</th>\n');
            fprintf(fid, '<th class="group-geometry">齿宽<br>系数</th>\n');
            fprintf(fid, '<th class="group-geometry">行星<br>轮数<br>量</th>\n');
            fprintf(fid, '<th class="group-shift">太阳<br>轮变<br>位系<br>数</th>\n');
            fprintf(fid, '<th class="group-shift">行星<br>轮变<br>位系<br>数</th>\n');
            fprintf(fid, '<th class="group-shift">内齿<br>圈变<br>位系<br>数</th>\n');
            fprintf(fid, '<th class="group-result">中心<br>距<br>(mm)</th>\n');
            fprintf(fid, '<th class="group-ratio">传动<br>比</th>\n');
            fprintf(fid, '<th class="group-safety">SHsps2</th>\n');
            fprintf(fid, '<th class="group-safety">SHspp2</th>\n');
            fprintf(fid, '<th class="group-safety">SFsps2</th>\n');
            fprintf(fid, '<th class="group-safety">SFspp2</th>\n');
            fprintf(fid, '<th class="group-safety">SHprr2</th>\n');
            fprintf(fid, '<th class="group-safety">SHprp2</th>\n');
            fprintf(fid, '<th class="group-safety">SFprr2</th>\n');
            fprintf(fid, '<th class="group-safety">SFprp2</th>\n');
            fprintf(fid, '<th class="group-mass">二级<br>太阳<br>轮<br>(kg)</th>\n');
            fprintf(fid, '<th class="group-mass">二级<br>行星<br>轮<br>(kg)</th>\n');
            fprintf(fid, '<th class="group-mass">二级<br>内齿<br>圈<br>(kg)</th>\n');
            % 三级齿轮系参数
            fprintf(fid, '<th class="group-geometry">太阳<br>轮齿<br>数</th>\n');
            fprintf(fid, '<th class="group-geometry">行星<br>轮齿<br>数</th>\n');
            fprintf(fid, '<th class="group-geometry">内齿<br>圈齿<br>数</th>\n');
            fprintf(fid, '<th class="group-geometry">模数<br>(mm)</th>\n');
            fprintf(fid, '<th class="group-geometry">压力<br>角<br>(°)</th>\n');
            fprintf(fid, '<th class="group-geometry">螺旋<br>角<br>(°)</th>\n');
            fprintf(fid, '<th class="group-geometry">齿宽<br>系数</th>\n');
            fprintf(fid, '<th class="group-geometry">行星<br>轮数<br>量</th>\n');
            fprintf(fid, '<th class="group-shift">太阳<br>轮变<br>位系<br>数</th>\n');
            fprintf(fid, '<th class="group-shift">行星<br>轮变<br>位系<br>数</th>\n');
            fprintf(fid, '<th class="group-shift">内齿<br>圈变<br>位系<br>数</th>\n');
            fprintf(fid, '<th class="group-result">中心<br>距<br>(mm)</th>\n');
            fprintf(fid, '<th class="group-ratio">传动<br>比</th>\n');
            fprintf(fid, '<th class="group-safety">SHsps3</th>\n');
            fprintf(fid, '<th class="group-safety">SHspp3</th>\n');
            fprintf(fid, '<th class="group-safety">SFsps3</th>\n');
            fprintf(fid, '<th class="group-safety">SFspp3</th>\n');
            fprintf(fid, '<th class="group-safety">SHprr3</th>\n');
            fprintf(fid, '<th class="group-safety">SHprp3</th>\n');
            fprintf(fid, '<th class="group-safety">SFprr3</th>\n');
            fprintf(fid, '<th class="group-safety">SFprp3</th>\n');
            fprintf(fid, '<th class="group-mass">三级<br>太阳<br>轮<br>(kg)</th>\n');
            fprintf(fid, '<th class="group-mass">三级<br>行星<br>轮<br>(kg)</th>\n');
            fprintf(fid, '<th class="group-mass">三级<br>内齿<br>圈<br>(kg)</th>\n');
            fprintf(fid, '</tr>\n');
            fprintf(fid, '</thead>\n');

            % 表格内容 - 显示所有解或空表格
            fprintf(fid, '<tbody>\n');

            if height(param_table) == 0
                % 如果没有数据，显示一行提示信息
                fprintf(fid, '<tr>\n');
                fprintf(fid, '<td colspan="57" style="text-align:center; color:#7f8c8d; font-style:italic; padding:20px;">该算法未产生有效解</td>\n');
                fprintf(fid, '</tr>\n');
            else
                % 显示所有解
                max_rows = height(param_table);

                for row_idx = 1:max_rows
                % 根据传动比误差和安全系数设置行的背景色
                % 使用列名来访问数据
                if ismember('Error', param_table.Properties.VariableNames)
                    ratio_error = param_table.Error(row_idx);
                else
                    ratio_error = 999; % 默认值，表示未知
                end

                if ismember('SH', param_table.Properties.VariableNames)
                    contact_safety = param_table.SH(row_idx);
                else
                    contact_safety = 0; % 默认值
                end

                if ismember('SF', param_table.Properties.VariableNames)
                    bending_safety = param_table.SF(row_idx);
                else
                    bending_safety = 0; % 默认值
                end

                % 检查是否为全局最优解
                is_global_best = false;
                if global_best_found && strcmp(alg_names{alg_idx}, global_best_alg_name)
                    % 通过参数匹配来确定是否为全局最优解
                    if ismember('z1', param_table.Properties.VariableNames) && ...
                       ismember('z2', param_table.Properties.VariableNames) && ...
                       ismember('zs2', param_table.Properties.VariableNames) && ...
                       ismember('zp2', param_table.Properties.VariableNames) && ...
                       ismember('zs3', param_table.Properties.VariableNames) && ...
                       ismember('zp3', param_table.Properties.VariableNames)

                        current_z1 = round(param_table.z1(row_idx));
                        current_z2 = round(param_table.z2(row_idx));
                        current_zs2 = round(param_table.zs2(row_idx));
                        current_zp2 = round(param_table.zp2(row_idx));
                        current_zs3 = round(param_table.zs3(row_idx));
                        current_zp3 = round(param_table.zp3(row_idx));

                        if current_z1 == global_best_z1 && current_z2 == global_best_z2 && ...
                           current_zs2 == global_best_zs2 && current_zp2 == global_best_zp2 && ...
                           current_zs3 == global_best_zs3 && current_zp3 == global_best_zp3
                            % 进一步检查质量是否匹配，确保是真正的全局最优解
                            if ismember('TotalMass', param_table.Properties.VariableNames)
                                current_mass = param_table.TotalMass(row_idx);
                                if abs(current_mass - global_best_mass) < 0.01  % 质量差小于0.01kg
                                    is_global_best = true;
                                end
                            end
                        end
                    end
                end

                % 确定序号列的背景颜色
                seq_style = 'border-right: 1px solid #e0e6ed;';
                % 使用局部变量来避免作用域问题
                local_contact_safety_factor = 1.2;
                local_bending_safety_factor = 1.2;
                if exist('contact_safety_factor', 'var')
                    local_contact_safety_factor = contact_safety_factor;
                end
                if exist('bending_safety_factor', 'var')
                    local_bending_safety_factor = bending_safety_factor;
                end

                % 序号列颜色标记逻辑
                if is_global_best
                    % 全局最优解：淡红色背景
                    seq_style = 'background-color:#ffcccc; border-right: 1px solid #e0e6ed;';
                elseif ratio_error <= 2.0 && contact_safety >= local_contact_safety_factor && bending_safety >= local_bending_safety_factor
                    % 满足所有约束条件的解：绿色背景
                    seq_style = 'background-color:#d5f4e6; border-right: 1px solid #e0e6ed;';
                elseif ratio_error <= 2.0
                    % 仅满足传动比约束的解：黄色背景
                    seq_style = 'background-color:#fff3cd; border-right: 1px solid #e0e6ed;';
                else
                    % 不满足任何约束的解：默认样式
                    seq_style = 'border-right: 1px solid #e0e6ed;';
                end

                fprintf(fid, '<tr>\n');
                fprintf(fid, '<td style="%s">%d</td>\n', seq_style, row_idx);

                % 使用列名来安全地访问数据 - 综合指标
                if ismember('TotalMass', param_table.Properties.VariableNames)
                    fprintf(fid, '<td class="group-comprehensive" style="border-right: 1px solid #e0e6ed;">%.2f</td>\n', param_table.TotalMass(row_idx));
                else
                    fprintf(fid, '<td class="group-comprehensive" style="border-right: 1px solid #e0e6ed;">-</td>\n');
                end

                fprintf(fid, '<td class="group-comprehensive" style="border-right: 1px solid #e0e6ed;">%.3f</td>\n', contact_safety);
                fprintf(fid, '<td class="group-comprehensive" style="border-right: 1px solid #e0e6ed;">%.3f</td>\n', bending_safety);

                % 总传动比
                if ismember('TotalRatio', param_table.Properties.VariableNames)
                    fprintf(fid, '<td class="group-comprehensive" style="border-right: 1px solid #e0e6ed;">%.3f</td>\n', param_table.TotalRatio(row_idx));
                else
                    fprintf(fid, '<td class="group-comprehensive" style="border-right: 1px solid #e0e6ed;">-</td>\n');
                end

                fprintf(fid, '<td class="group-comprehensive" style="border-right: 1px solid #3d5a7a;">%.3f</td>\n', ratio_error);

                % 一级齿轮系参数 (按新的顺序：齿数、模数、角度、齿宽系数、变位系数、中心距、传动比、安全系数、质量)
                stage1_params = {'z1', 'z2', 'm1', 'alpha1', 'beta1', 'k_h1', 'x1', 'x2', 'a1', 'i1', 'SH1', 'SF1', 'SF2', 'M1', 'M2'};
                for param_name = stage1_params
                    % 确定CSS类
                    css_class = 'group-geometry'; % 默认为几何参数
                    if strcmp(param_name{1}, 'i1')
                        css_class = 'group-ratio';
                    elseif contains(param_name{1}, {'x1', 'x2'})
                        css_class = 'group-shift';
                    elseif strcmp(param_name{1}, 'a1')
                        css_class = 'group-result';
                    elseif contains(param_name{1}, {'SH1', 'SF1', 'SF2'})
                        css_class = 'group-safety';
                    elseif contains(param_name{1}, {'M1', 'M2'})
                        css_class = 'group-mass';
                    end

                    if ismember(param_name{1}, param_table.Properties.VariableNames)
                        if strcmp(param_name{1}, 'i1')
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.3f</td>\n', css_class, param_table.(param_name{1})(row_idx));
                        elseif strcmp(param_name{1}, 'm1')
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.0f</td>\n', css_class, round(param_table.(param_name{1})(row_idx)));
                        elseif contains(param_name{1}, {'z1', 'z2'})
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%d</td>\n', css_class, round(param_table.(param_name{1})(row_idx)));
                        elseif contains(param_name{1}, {'x1', 'x2'})
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.4f</td>\n', css_class, param_table.(param_name{1})(row_idx));
                        elseif contains(param_name{1}, {'alpha1', 'beta1'})
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.1f</td>\n', css_class, param_table.(param_name{1})(row_idx));
                        elseif strcmp(param_name{1}, 'a1')
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.2f</td>\n', css_class, param_table.(param_name{1})(row_idx));
                        elseif contains(param_name{1}, {'k_h1', 'SH1', 'SF1', 'SF2', 'M1'})
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.3f</td>\n', css_class, param_table.(param_name{1})(row_idx));
                        elseif strcmp(param_name{1}, 'M2')
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #3d5a7a;">%.3f</td>\n', css_class, param_table.(param_name{1})(row_idx));
                        else
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.2f</td>\n', css_class, param_table.(param_name{1})(row_idx));
                        end
                    else
                        % 对于缺失的列，尝试使用备用列名或计算值
                        if strcmp(param_name{1}, 'alpha1')
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">20.0</td>\n', css_class); % 一级压力角固定为20°
                        elseif strcmp(param_name{1}, 'M2')
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #3d5a7a;">-</td>\n', css_class);
                        else
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">-</td>\n', css_class);
                        end
                    end
                end

                % 二级齿轮系参数 (按新的顺序：齿数、模数、角度、齿宽系数、行星轮数量、变位系数、中心距、传动比、安全系数、质量)
                stage2_params = {'zs2', 'zp2', 'zr2', 'mn2', 'alpha2', 'beta2', 'k_h2', 'n2', 'xs2', 'xp2', 'xr2', 'a2', 'i2', 'SHsps2', 'SHspp2', 'SFsps2', 'SFspp2', 'SHprr2', 'SHprp2', 'SFprr2', 'SFprp2', 'Ms2', 'Mp2', 'Mr2'};
                for param_name = stage2_params
                    % 确定CSS类
                    css_class = 'group-geometry'; % 默认为几何参数
                    if strcmp(param_name{1}, 'i2')
                        css_class = 'group-ratio';
                    elseif contains(param_name{1}, {'xs2', 'xp2', 'xr2'})
                        css_class = 'group-shift';
                    elseif strcmp(param_name{1}, 'a2')
                        css_class = 'group-result';
                    elseif contains(param_name{1}, {'SHsps2', 'SHspp2', 'SFsps2', 'SFspp2', 'SHprr2', 'SHprp2', 'SFprr2', 'SFprp2'})
                        css_class = 'group-safety';
                    elseif contains(param_name{1}, {'Ms2', 'Mp2', 'Mr2'})
                        css_class = 'group-mass';
                    end

                    if ismember(param_name{1}, param_table.Properties.VariableNames)
                        if strcmp(param_name{1}, 'i2')
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.3f</td>\n', css_class, param_table.(param_name{1})(row_idx));
                        elseif strcmp(param_name{1}, 'mn2')
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.0f</td>\n', css_class, round(param_table.(param_name{1})(row_idx)));
                        elseif contains(param_name{1}, {'zs2', 'zp2', 'zr2', 'n2'})
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%d</td>\n', css_class, round(param_table.(param_name{1})(row_idx)));
                        elseif contains(param_name{1}, {'xs2', 'xp2', 'xr2'})
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.4f</td>\n', css_class, param_table.(param_name{1})(row_idx));
                        elseif contains(param_name{1}, {'alpha2', 'beta2'})
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.1f</td>\n', css_class, param_table.(param_name{1})(row_idx));
                        elseif strcmp(param_name{1}, 'a2')
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.2f</td>\n', css_class, param_table.(param_name{1})(row_idx));
                        elseif contains(param_name{1}, {'k_h2', 'SHsps2', 'SHspp2', 'SFsps2', 'SFspp2', 'SHprr2', 'SHprp2', 'SFprr2', 'SFprp2', 'Ms2', 'Mp2'})
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.3f</td>\n', css_class, param_table.(param_name{1})(row_idx));
                        elseif strcmp(param_name{1}, 'Mr2')
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #3d5a7a;">%.3f</td>\n', css_class, param_table.(param_name{1})(row_idx));
                        else
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.2f</td>\n', css_class, param_table.(param_name{1})(row_idx));
                        end
                    else
                        % 对于缺失的列，尝试使用备用列名或计算值
                        if strcmp(param_name{1}, 'alpha2')
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">20.0</td>\n', css_class); % 二级压力角固定为20°
                        elseif strcmp(param_name{1}, 'beta2')
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">0.0</td>\n', css_class); % 二级螺旋角固定为0°
                        elseif strcmp(param_name{1}, 'Mr2')
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #3d5a7a;">-</td>\n', css_class);
                        else
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">-</td>\n', css_class);
                        end
                    end
                end

                % 三级齿轮系参数 (按新的顺序：齿数、模数、角度、齿宽系数、行星轮数量、变位系数、中心距、传动比、安全系数、质量)
                stage3_params = {'zs3', 'zp3', 'zr3', 'mn3', 'alpha3', 'beta3', 'k_h3', 'n3', 'xs3', 'xp3', 'xr3', 'a3', 'i3', 'SHsps3', 'SHspp3', 'SFsps3', 'SFspp3', 'SHprr3', 'SHprp3', 'SFprr3', 'SFprp3', 'Ms3', 'Mp3', 'Mr3'};
                for param_name = stage3_params
                    % 确定CSS类
                    css_class = 'group-geometry'; % 默认为几何参数
                    if strcmp(param_name{1}, 'i3')
                        css_class = 'group-ratio';
                    elseif contains(param_name{1}, {'xs3', 'xp3', 'xr3'})
                        css_class = 'group-shift';
                    elseif strcmp(param_name{1}, 'a3')
                        css_class = 'group-result';
                    elseif contains(param_name{1}, {'SHsps3', 'SHspp3', 'SFsps3', 'SFspp3', 'SHprr3', 'SHprp3', 'SFprr3', 'SFprp3'})
                        css_class = 'group-safety';
                    elseif contains(param_name{1}, {'Ms3', 'Mp3', 'Mr3'})
                        css_class = 'group-mass';
                    end

                    if ismember(param_name{1}, param_table.Properties.VariableNames)
                        if strcmp(param_name{1}, 'i3')
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.3f</td>\n', css_class, param_table.(param_name{1})(row_idx));
                        elseif strcmp(param_name{1}, 'mn3')
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.0f</td>\n', css_class, round(param_table.(param_name{1})(row_idx)));
                        elseif contains(param_name{1}, {'zs3', 'zp3', 'zr3', 'n3'})
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%d</td>\n', css_class, round(param_table.(param_name{1})(row_idx)));
                        elseif contains(param_name{1}, {'xs3', 'xp3', 'xr3'})
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.4f</td>\n', css_class, param_table.(param_name{1})(row_idx));
                        elseif contains(param_name{1}, {'alpha3', 'beta3'})
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.1f</td>\n', css_class, param_table.(param_name{1})(row_idx));
                        elseif strcmp(param_name{1}, 'a3')
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.2f</td>\n', css_class, param_table.(param_name{1})(row_idx));
                        elseif contains(param_name{1}, {'k_h3', 'SHsps3', 'SHspp3', 'SFsps3', 'SFspp3', 'SHprr3', 'SHprp3', 'SFprr3', 'SFprp3', 'Ms3', 'Mp3', 'Mr3'})
                            if strcmp(param_name{1}, 'Mr3')
                                fprintf(fid, '<td class="%s">%.3f</td>\n', css_class, param_table.(param_name{1})(row_idx)); % 最后一列不需要右边框
                            else
                                fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.3f</td>\n', css_class, param_table.(param_name{1})(row_idx));
                            end
                        else
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">%.2f</td>\n', css_class, param_table.(param_name{1})(row_idx));
                        end
                    else
                        % 对于缺失的列，尝试使用备用列名或计算值
                        if strcmp(param_name{1}, 'beta3')
                            fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">0.0</td>\n', css_class); % 三级螺旋角固定为0°
                        else
                            if strcmp(param_name{1}, 'Mr3')
                                fprintf(fid, '<td class="%s">-</td>\n', css_class); % 最后一列不需要右边框
                            else
                                fprintf(fid, '<td class="%s" style="border-right: 1px solid #e0e6ed;">-</td>\n', css_class);
                            end
                        end
                    end
                end

                fprintf(fid, '</tr>\n');
                end
            end

            fprintf(fid, '</tbody>\n');
            fprintf(fid, '</table>\n');
            fprintf(fid, '</div>\n');

            % 显示解的总数信息
            fprintf(fid, '<p style="color:#7f8c8d; font-style:italic;">注：共显示 %d 个解。</p>\n', height(param_table));

            % 添加颜色说明
            fprintf(fid, '<div style="margin-top:10px; padding:10px; background-color:#f8f9fa; border-radius:5px;">\n');
            fprintf(fid, '<p style="margin:0; font-size:12px;"><strong>颜色说明：</strong></p>\n');

            % 检查当前算法是否包含全局最优解
            has_global_best = false;
            if global_best_found && strcmp(alg_names{alg_idx}, global_best_alg_name)
                has_global_best = true;
            end

            % 如果当前算法包含全局最优解，则显示红色标记说明
            if has_global_best
                fprintf(fid, '<p style="margin:5px 0; font-size:12px;"><span style="display:inline-block; width:20px; height:15px; background-color:#ffcccc; border:1px solid #ccc; margin-right:5px;"></span><strong>全局最优解</strong>（综合考虑质量、安全系数和传动比误差的最佳方案）</p>\n');
            end



            % 使用局部变量来避免作用域问题
            local_contact_safety_factor = 1.2;
            local_bending_safety_factor = 1.2;
            if exist('contact_safety_factor', 'var')
                local_contact_safety_factor = contact_safety_factor;
            end
            if exist('bending_safety_factor', 'var')
                local_bending_safety_factor = bending_safety_factor;
            end
            fprintf(fid, '<p style="margin:5px 0; font-size:12px;"><span style="display:inline-block; width:20px; height:15px; background-color:#d5f4e6; border:1px solid #ccc; margin-right:5px;"></span>满足所有约束条件的解（传动比误差≤2%% 且 接触安全系数≥%.1f 且 弯曲安全系数≥%.1f）</p>\n', local_contact_safety_factor, local_bending_safety_factor);
            fprintf(fid, '<p style="margin:5px 0; font-size:12px;"><span style="display:inline-block; width:20px; height:15px; background-color:#fff3cd; border:1px solid #ccc; margin-right:5px;"></span>仅满足传动比约束的解</p>\n');
            fprintf(fid, '<p style="margin:0; font-size:12px;"><span style="display:inline-block; width:20px; height:15px; background-color:white; border:1px solid #ccc; margin-right:5px;"></span>不满足传动比约束和安全系数约束的解</p>\n');
            fprintf(fid, '</div>\n');

            % 添加参数分类说明 - 横向布局
            fprintf(fid, '<div style="margin-top:10px; padding:10px; background-color:#f8f9fa; border-radius:5px;">\n');
            fprintf(fid, '<p style="margin:0 0 8px 0; font-size:12px;"><strong>表格颜色说明：</strong></p>\n');
            fprintf(fid, '<div style="display:flex; flex-wrap:wrap; gap:15px; align-items:center;">\n');
            fprintf(fid, '<div style="display:flex; align-items:center;"><span style="display:inline-block; width:20px; height:15px; background-color:#f8f9fa; border:1px solid #ccc; margin-right:5px;"></span><span style="font-size:12px;">综合指标</span></div>\n');
            fprintf(fid, '<div style="display:flex; align-items:center;"><span style="display:inline-block; width:20px; height:15px; background-color:#e8f4f8; border:1px solid #ccc; margin-right:5px;"></span><span style="font-size:12px;">几何参数</span></div>\n');
            fprintf(fid, '<div style="display:flex; align-items:center;"><span style="display:inline-block; width:20px; height:15px; background-color:#f0f8e8; border:1px solid #ccc; margin-right:5px;"></span><span style="font-size:12px;">变位系数</span></div>\n');
            fprintf(fid, '<div style="display:flex; align-items:center;"><span style="display:inline-block; width:20px; height:15px; background-color:#fffcf0; border:1px solid #ccc; margin-right:5px;"></span><span style="font-size:12px;">结果参数</span></div>\n');
            fprintf(fid, '<div style="display:flex; align-items:center;"><span style="display:inline-block; width:20px; height:15px; background-color:#f3e5f5; border:1px solid #ccc; margin-right:5px;"></span><span style="font-size:12px;">安全系数</span></div>\n');
            fprintf(fid, '<div style="display:flex; align-items:center;"><span style="display:inline-block; width:20px; height:15px; background-color:#ffeee6; border:1px solid #ccc; margin-right:5px;"></span><span style="font-size:12px;">质量参数</span></div>\n');
            fprintf(fid, '</div>\n');
            fprintf(fid, '</div>\n');

        catch e
            fprintf(fid, '<p style="color:#e74c3c;">生成该算法的参数表格时出错：%s</p>\n', e.message);
        end

        % 算法之间的分隔线
        if alg_idx < length(alg_names)
            fprintf(fid, '<hr style="margin:30px 0; border:none; border-top:2px solid #ecf0f1;">\n');
        end
    end

    fprintf(fid, '</div>\n');

    % 添加JavaScript代码实现平滑滚动和DataTables初始化
    fprintf(fid, '<script>\n');
    fprintf(fid, '$(document).ready(function() {\n');

    % 初始化所有DataTables
    fprintf(fid, '    // 初始化DataTables\n');
    for alg_idx = 1:length(alg_names)
        fprintf(fid, '    if ($("#algorithmTable_%d").length) {\n', alg_idx);
        fprintf(fid, '        $("#algorithmTable_%d").DataTable({\n', alg_idx);
        fprintf(fid, '            "language": {\n');
        fprintf(fid, '                "search": "搜索:",\n');
        fprintf(fid, '                "lengthMenu": "每页显示 _MENU_ 条记录",\n');
        fprintf(fid, '                "zeroRecords": "没有找到匹配的记录",\n');
        fprintf(fid, '                "info": "显示第 _START_ 至 _END_ 条记录，共 _TOTAL_ 条",\n');
        fprintf(fid, '                "infoEmpty": "显示第 0 至 0 条记录，共 0 条",\n');
        fprintf(fid, '                "infoFiltered": "(从 _MAX_ 条记录过滤)",\n');
        fprintf(fid, '                "paginate": {\n');
        fprintf(fid, '                    "first": "首页",\n');
        fprintf(fid, '                    "last": "末页",\n');
        fprintf(fid, '                    "next": "下一页",\n');
        fprintf(fid, '                    "previous": "上一页"\n');
        fprintf(fid, '                }\n');
        fprintf(fid, '            },\n');
        fprintf(fid, '            "pageLength": 10,\n');
        fprintf(fid, '            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "全部"]],\n');
        fprintf(fid, '            "order": [[1, "asc"]],\n');
        fprintf(fid, '            "scrollX": true,\n');
        fprintf(fid, '            "autoWidth": false\n');
        fprintf(fid, '        });\n');
        fprintf(fid, '    }\n');
    end

    fprintf(fid, '    \n');
    fprintf(fid, '    // 平滑滚动到锚点\n');
    fprintf(fid, '    $(".navbar a").on("click", function(e) {\n');
    fprintf(fid, '        e.preventDefault();\n');
    fprintf(fid, '        var target = $(this).attr("href");\n');
    fprintf(fid, '        if ($(target).length) {\n');
    fprintf(fid, '            $("html, body").animate({\n');
    fprintf(fid, '                scrollTop: $(target).offset().top - 70\n');
    fprintf(fid, '            }, 600);\n');
    fprintf(fid, '        }\n');
    fprintf(fid, '    });\n');
    fprintf(fid, '    \n');
    fprintf(fid, '    // 高亮当前导航项\n');
    fprintf(fid, '    $(window).scroll(function() {\n');
    fprintf(fid, '        var scrollPos = $(document).scrollTop() + 100;\n');
    fprintf(fid, '        $(".navbar a").each(function() {\n');
    fprintf(fid, '            var currLink = $(this);\n');
    fprintf(fid, '            var refElement = $(currLink.attr("href"));\n');
    fprintf(fid, '            if (refElement.length && refElement.position().top <= scrollPos && refElement.position().top + refElement.height() > scrollPos) {\n');
    fprintf(fid, '                $(".navbar a").removeClass("active");\n');
    fprintf(fid, '                currLink.addClass("active");\n');
    fprintf(fid, '            }\n');
    fprintf(fid, '        });\n');
    fprintf(fid, '    });\n');
    fprintf(fid, '});\n');
    fprintf(fid, '</script>\n');

    % 结束HTML文件
    fprintf(fid, '</div>\n'); % 结束main-container div
    fprintf(fid, '</body>\n');
    fprintf(fid, '</html>\n');
    
    % 关闭文件
    fclose(fid);

    % 显示生成的HTML文件路径
    disp(['已生成HTML报告: ' html_file]);

end